#!/usr/bin/env python3
"""
RoraFTP Optimized Launcher
High-performance cloud file server
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Launch RoraFTP with optimized settings."""
    print("🚀 Starting RoraFTP Optimized Server...")
    
    # Default optimized settings
    args = [
        sys.executable,
        "roraftp_server.py",
        "--port", "8888",
        "--ftp-port", "2122",
        "--share", "./shared_files"
    ]
    
    # Add any command line arguments
    args.extend(sys.argv[1:])
    
    try:
        subprocess.run(args, cwd=Path(__file__).parent)
    except KeyboardInterrupt:
        print("\n👋 RoraFTP server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
