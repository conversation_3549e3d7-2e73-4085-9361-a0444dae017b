#!/usr/bin/env python3
"""
Minimal test server to isolate performance issues
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import time

class MinimalHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Minimal GET handler."""
        start_time = time.time()
        
        # Send minimal response
        response = b"Hello World"
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.send_header('Content-Length', str(len(response)))
        self.end_headers()
        self.wfile.write(response)
        
        end_time = time.time()
        print(f"Request processed in {(end_time - start_time)*1000:.2f}ms")

def main():
    server = HTTPServer(('localhost', 9999), MinimalHandler)
    print("Minimal test server running on http://localhost:9999")
    print("Press Ctrl+C to stop")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")

if __name__ == "__main__":
    main()
