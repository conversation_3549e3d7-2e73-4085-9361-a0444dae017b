#!/usr/bin/env python3
"""
RoraFTP Test Suite
Basic functionality tests for the Universal File Server
"""

import os
import sys
import time
import threading
import requests
import tempfile
import shutil
from pathlib import Path
import subprocess
import json

# Add current directory to path to import the server
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_test_status(message, status="info"):
    """Print test status with colors."""
    icons = {"success": "✅", "error": "❌", "warning": "⚠️", "info": "ℹ️"}
    icon = icons.get(status, "ℹ️")
    print(f"{icon} {message}")

class RoraFTPTester:
    """Test suite for RoraFTP server."""
    
    def __init__(self):
        self.test_port = 8888  # Use different port for testing
        self.test_ftp_port = 2121
        self.test_dir = None
        self.server_process = None
        self.base_url = f"http://localhost:{self.test_port}"
        
    def setup(self):
        """Set up test environment."""
        print_test_status("Setting up test environment...")
        
        # Create temporary directory for testing
        self.test_dir = tempfile.mkdtemp(prefix="roraftp_test_")
        print_test_status(f"Test directory: {self.test_dir}")
        
        # Create test files
        test_file = Path(self.test_dir) / "test.txt"
        with open(test_file, 'w') as f:
            f.write("This is a test file for RoraFTP\n")
        
        print_test_status("Test environment ready", "success")
        
    def teardown(self):
        """Clean up test environment."""
        print_test_status("Cleaning up test environment...")
        
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
        
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
        
        print_test_status("Cleanup complete", "success")
    
    def start_server(self):
        """Start the RoraFTP server for testing."""
        print_test_status("Starting test server...")

        cmd = [
            sys.executable, "quick_ftp_server.py",
            "--port", str(self.test_port),
            "--ftp-port", str(self.test_ftp_port),
            "--share", self.test_dir,
            "--no-browser",
            "--no-qr"
        ]

        try:
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Capture both stdout and stderr
                text=True
            )

            # Wait for server to start and check output
            time.sleep(5)  # Give more time to start

            if self.server_process.poll() is None:
                print_test_status("Test server started", "success")
                return True
            else:
                # Server exited, get the output
                stdout, _ = self.server_process.communicate()
                print_test_status(f"Server failed to start. Output: {stdout}", "error")
                return False

        except Exception as e:
            print_test_status(f"Failed to start server: {e}", "error")
            return False
    
    def test_health_check(self):
        """Test the health check endpoint."""
        print_test_status("Testing health check endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    print_test_status("Health check passed", "success")
                    return True
                else:
                    print_test_status(f"Health check failed: {data}", "error")
                    return False
            else:
                print_test_status(f"Health check returned {response.status_code}", "error")
                return False
                
        except Exception as e:
            print_test_status(f"Health check failed: {e}", "error")
            return False
    
    def test_web_interface(self):
        """Test the web interface."""
        print_test_status("Testing web interface...")

        try:
            response = requests.get(self.base_url, timeout=10)

            if response.status_code == 200:
                content = response.text
                # Check for key elements that should be in the web interface
                if "Cloud Storage" in content:
                    print_test_status("Web interface test passed", "success")
                    return True
                else:
                    print_test_status(f"Web interface content check failed. Content length: {len(content)}", "error")
                    # Print first 500 chars for debugging
                    print_test_status(f"Content preview: {content[:500]}...", "info")
                    return False
            else:
                print_test_status(f"Web interface returned {response.status_code}", "error")
                return False

        except Exception as e:
            print_test_status(f"Web interface test failed: {e}", "error")
            return False
    
    def test_file_download(self):
        """Test file download."""
        print_test_status("Testing file download...")
        
        try:
            response = requests.get(f"{self.base_url}/test.txt", timeout=5)
            
            if response.status_code == 200:
                content = response.text
                if "This is a test file for RoraFTP" in content:
                    print_test_status("File download test passed", "success")
                    return True
                else:
                    print_test_status("Downloaded file content incorrect", "error")
                    return False
            else:
                print_test_status(f"File download returned {response.status_code}", "error")
                return False
                
        except Exception as e:
            print_test_status(f"File download test failed: {e}", "error")
            return False
    
    def test_file_upload(self):
        """Test file upload."""
        print_test_status("Testing file upload...")
        
        try:
            # Create a test file to upload
            test_content = "This is an uploaded test file"
            files = {'file': ('upload_test.txt', test_content, 'text/plain')}
            
            response = requests.post(f"{self.base_url}/upload", files=files, timeout=10)
            
            if response.status_code == 200:
                # Check if file was actually uploaded
                uploaded_file = Path(self.test_dir) / "upload_test.txt"
                if uploaded_file.exists():
                    with open(uploaded_file, 'r') as f:
                        if f.read() == test_content:
                            print_test_status("File upload test passed", "success")
                            return True
                        else:
                            print_test_status("Uploaded file content incorrect", "error")
                            return False
                else:
                    print_test_status("Uploaded file not found", "error")
                    return False
            else:
                print_test_status(f"File upload returned {response.status_code}", "error")
                return False
                
        except Exception as e:
            print_test_status(f"File upload test failed: {e}", "error")
            return False
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        print_test_status("Testing rate limiting...")
        
        try:
            # Make multiple rapid requests
            responses = []
            for i in range(60):  # Exceed the default rate limit
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=1)
                    responses.append(response.status_code)
                except:
                    responses.append(0)
            
            # Check if we got rate limited (429 status)
            if 429 in responses:
                print_test_status("Rate limiting test passed", "success")
                return True
            else:
                print_test_status("Rate limiting not triggered (may be expected)", "warning")
                return True  # Not necessarily a failure
                
        except Exception as e:
            print_test_status(f"Rate limiting test failed: {e}", "error")
            return False
    
    def run_all_tests(self):
        """Run all tests."""
        print_test_status("🧪 Starting RoraFTP Test Suite")
        print("=" * 50)
        
        # Setup
        self.setup()
        
        # Start server
        if not self.start_server():
            self.teardown()
            return False
        
        # Run tests
        tests = [
            ("Health Check", self.test_health_check),
            ("Web Interface", self.test_web_interface),
            ("File Download", self.test_file_download),
            ("File Upload", self.test_file_upload),
            ("Rate Limiting", self.test_rate_limiting),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n--- {test_name} ---")
            if test_func():
                passed += 1
            else:
                print_test_status(f"{test_name} failed", "error")
        
        # Results
        print("\n" + "=" * 50)
        print_test_status(f"Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print_test_status("🎉 All tests passed!", "success")
            success = True
        else:
            print_test_status(f"❌ {total - passed} tests failed", "error")
            success = False
        
        # Cleanup
        self.teardown()
        
        return success

def main():
    """Main test function."""
    if not os.path.exists("quick_ftp_server.py"):
        print_test_status("quick_ftp_server.py not found in current directory", "error")
        print_test_status("Please run tests from the RoraFTP directory", "info")
        return False
    
    # Check if requests is available
    try:
        import requests
    except ImportError:
        print_test_status("Installing requests for testing...", "info")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
            import requests
            print_test_status("Requests installed successfully", "success")
        except Exception as e:
            print_test_status(f"Failed to install requests: {e}", "error")
            return False
    
    # Run tests
    tester = RoraFTPTester()
    return tester.run_all_tests()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
