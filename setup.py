#!/usr/bin/env python3
"""
RoraFTP - Universal File Server
Setup script for package installation
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README for long description
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith('#')
        ]

setup(
    name="roraftp",
    version="1.0.0",
    author="ForgottenCNZ",
    author_email="<EMAIL>",
    description="Production-ready universal file server for all devices",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ForgottenCNZ/RoraFTP",
    project_urls={
        "Bug Reports": "https://github.com/ForgottenCNZ/RoraFTP/issues",
        "Source": "https://github.com/ForgottenCNZ/RoraFTP",
        "Documentation": "https://github.com/ForgottenCNZ/RoraFTP/wiki",
    },
    
    # Package information
    packages=find_packages(),
    py_modules=["quick_ftp_server"],
    
    # Requirements
    python_requires=">=3.6",
    install_requires=requirements,
    
    # Optional dependencies
    extras_require={
        "qr": ["qrcode[pil]>=7.0.0"],
        "ftp": ["pyftpdlib>=1.5.6"],
        "all": ["qrcode[pil]>=7.0.0", "pyftpdlib>=1.5.6"],
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.0.0",
            "black>=21.0.0",
            "flake8>=3.8.0",
            "mypy>=0.800",
        ],
    },
    
    # Entry points
    entry_points={
        "console_scripts": [
            "roraftp=quick_ftp_server:main",
            "roraftp-server=quick_ftp_server:main",
        ],
    },
    
    # Package data
    include_package_data=True,
    package_data={
        "": ["*.json", "*.md", "*.txt", "*.yml", "*.yaml"],
    },
    
    # Metadata
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: Internet :: File Transfer Protocol (FTP)",
        "Topic :: System :: Networking",
        "Topic :: Utilities",
        "Environment :: Web Environment",
        "Environment :: Console",
    ],
    
    keywords=[
        "file-server", "ftp", "http", "mobile", "universal", 
        "cross-platform", "production", "secure", "simple"
    ],
    
    # License
    license="MIT",
    
    # Zip safe
    zip_safe=False,
)
