# Changelog

All notable changes to RoraFTP will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-01

### Added
- 🚀 Initial release of RoraFTP Universal File Server
- 🌐 HTTP server with mobile-friendly web interface
- 📁 FTP server with anonymous access
- 📱 Full iPhone Safari compatibility with drag-and-drop
- 🤖 Android Chrome native support
- 💻 Desktop browser compatibility (all modern browsers)
- 📱 iOS Files app integration via FTP
- 🔧 FileZilla and other FTP client support

### Features
- **Multi-threaded Architecture**: Supports up to 50 concurrent connections
- **Rate Limiting**: Configurable request throttling (default: 50 req/min)
- **File Validation**: Secure file type and size checking
- **Streaming Uploads**: Memory-efficient large file handling
- **QR Code Generation**: Instant mobile connection via camera scan
- **Auto-Discovery**: Automatic network configuration
- **Responsive Design**: Touch-friendly interface for all screen sizes
- **Progress Indicators**: Real-time upload progress tracking
- **Compression**: Gzip compression for improved performance

### Security
- File type whitelist with safe extensions only
- Filename sanitization to prevent path traversal
- Configurable file size limits (default: 100MB)
- Rate limiting to prevent abuse
- No arbitrary code execution capabilities
- Secure HTTP headers

### Deployment
- **Docker Support**: Multi-stage Dockerfile with security best practices
- **Docker Compose**: Complete orchestration with optional services
- **Systemd Service**: Linux service integration
- **Cross-Platform Scripts**: Windows (.bat) and Unix (.sh) startup scripts
- **Environment Configuration**: Comprehensive .env support
- **Package Installation**: setuptools integration for pip install

### Documentation
- Comprehensive README with quick start guide
- Detailed deployment guide for various platforms
- Complete API documentation
- Configuration examples for different use cases
- Troubleshooting guide

### Supported Platforms
- **Operating Systems**: Windows, macOS, Linux, Docker
- **Python Versions**: 3.6, 3.7, 3.8, 3.9, 3.10, 3.11
- **Mobile Devices**: iPhone, iPad, Android phones/tablets
- **Browsers**: Chrome, Safari, Firefox, Edge, Opera
- **FTP Clients**: FileZilla, WinSCP, iOS Files, Android file managers

### File Types Supported
- **Documents**: .txt, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx
- **Images**: .jpg, .jpeg, .png, .gif, .bmp, .svg, .webp
- **Video**: .mp4, .avi, .mov
- **Audio**: .mp3, .wav, .flac
- **Archives**: .zip, .rar, .7z, .tar, .gz
- **Web**: .html, .css, .js, .json, .csv, .xml

### Dependencies
- **Core**: Python 3.6+ standard library only
- **Optional**: qrcode[pil] for QR code generation
- **Optional**: pyftpdlib for FTP server functionality
- **Auto-Installation**: Dependencies installed automatically when needed

### Configuration Options
- Configurable HTTP and FTP ports
- Custom share folder paths
- Adjustable file size limits
- Rate limiting configuration
- Feature toggles (FTP, QR codes, browser auto-open)
- Environment variable support
- JSON configuration files

### Performance
- Multi-threaded request handling
- Streaming file uploads/downloads
- Gzip compression for responses
- Efficient memory usage for large files
- Configurable chunk sizes
- Connection pooling

### Monitoring
- Comprehensive logging with device detection
- Health check endpoint for monitoring
- Security event logging
- Performance metrics
- Error tracking and reporting

## [Unreleased]

### Planned Features
- [ ] HTTPS/SSL support with automatic certificate generation
- [ ] User authentication and authorization
- [ ] File versioning and backup
- [ ] Bandwidth throttling
- [ ] Advanced file search and filtering
- [ ] Thumbnail generation for images
- [ ] Video streaming capabilities
- [ ] WebDAV protocol support
- [ ] API key authentication
- [ ] Webhook notifications
- [ ] Prometheus metrics export
- [ ] Database backend for metadata
- [ ] Clustering and load balancing
- [ ] Advanced security scanning
- [ ] Mobile app development

### Known Issues
- FTP passive mode may require firewall configuration
- Large file uploads on slow connections may timeout
- QR code generation requires additional dependencies

### Breaking Changes
None in this release.

---

## Version History

- **v1.0.0** - Initial production release
- **v0.9.0** - Beta release with core functionality
- **v0.8.0** - Alpha release for testing
- **v0.7.0** - Development preview
- **v0.6.0** - Proof of concept

## Support

For support, bug reports, and feature requests:
- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/ForgottenCNZ/RoraFTP/issues)
- 📖 Documentation: [GitHub Wiki](https://github.com/ForgottenCNZ/RoraFTP/wiki)
- 💬 Discussions: [GitHub Discussions](https://github.com/ForgottenCNZ/RoraFTP/discussions)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
