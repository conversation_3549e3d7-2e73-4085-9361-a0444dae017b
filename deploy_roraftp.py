#!/usr/bin/env python3
"""
🚀 RoraFTP Deployment Script
Version: 2.0.0
Author: ForgottenCNZ

One-click deployment script for RoraFTP Cloud File Server.
Creates directories, sets up configuration, and launches the server.
"""

import os
import sys
import json
import socket
import subprocess
import webbrowser
from pathlib import Path
import time
import shutil

# Colors for terminal output
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_banner():
    """Print the RoraFTP banner."""
    banner = f"""
{Colors.CYAN}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 RoraFTP - Production-Ready Cloud File Server          ║
║                                                              ║
║    ✨ High-Performance Features:                             ║
║    • Dynamic chunk sizing (64KB → 4MB adaptive)             ║
║    • HTTP keep-alive & smart caching                        ║
║    • Range requests & resume downloads                      ║
║    • Up to 10GB file support                                ║
║    • Beautiful cloud-themed interface                       ║
║                                                              ║
║    📱 Universal Device Support:                              ║
║    • iPhone Safari (drag & drop)                            ║
║    • Android Chrome (full compatibility)                    ║
║    • Desktop browsers (all modern)                          ║
║    • iOS Files app (FTP support)                            ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
{Colors.END}
"""
    print(banner)

def print_step(step_num, total_steps, message):
    """Print a deployment step."""
    print(f"{Colors.BLUE}[{step_num}/{total_steps}]{Colors.END} {Colors.WHITE}{message}{Colors.END}")

def print_success(message):
    """Print a success message."""
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")

def print_error(message):
    """Print an error message."""
    print(f"{Colors.RED}❌ {message}{Colors.END}")

def print_warning(message):
    """Print a warning message."""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")

def print_info(message):
    """Print an info message."""
    print(f"{Colors.CYAN}ℹ️  {message}{Colors.END}")

def get_local_ip():
    """Get local IP address."""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print_error("Python 3.6+ is required!")
        print_info("Please upgrade your Python installation.")
        return False
    print_success(f"Python {sys.version.split()[0]} detected")
    return True

def check_roraftp_exists():
    """Check if RoraFTP main file exists."""
    if not Path('quick_ftp_server.py').exists():
        print_error("RoraFTP main file (quick_ftp_server.py) not found!")
        print_info("Please ensure you're running this script from the RoraFTP directory.")
        return False
    print_success("RoraFTP main file found")
    return True

def create_directories(base_path):
    """Create necessary directories."""
    directories = [
        base_path,
        base_path / 'uploads',
        base_path / 'downloads', 
        base_path / 'shared',
        base_path / 'logs'
    ]
    
    created_dirs = []
    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            created_dirs.append(str(directory))
            print_success(f"Created directory: {directory}")
        except Exception as e:
            print_error(f"Failed to create directory {directory}: {e}")
            return None
    
    return created_dirs

def create_sample_files(base_path):
    """Create sample files for demonstration."""
    try:
        # Create a welcome file
        welcome_file = base_path / 'Welcome_to_RoraFTP.txt'
        welcome_content = f"""🚀 Welcome to RoraFTP Cloud File Server!

This is your personal cloud storage powered by RoraFTP v2.0.

✨ Features:
• Upload files up to 10GB each
• Beautiful cloud-themed interface  
• Works on iPhone, Android, and desktop
• High-performance with smart caching
• Resume downloads with range requests

📱 Mobile Access:
• Open this URL in any mobile browser
• iOS users can also use the Files app with FTP

🔧 Performance Optimizations:
• Dynamic chunk sizing (64KB → 4MB)
• HTTP keep-alive connections
• Smart caching for faster access
• Metadata caching for quick directory browsing

Created: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        welcome_file.write_text(welcome_content, encoding='utf-8')
        print_success("Created welcome file")
        
        # Create a README for uploads
        uploads_readme = base_path / 'uploads' / 'README.txt'
        uploads_readme.write_text("📤 Upload your files here!\n\nThis folder is for file uploads.", encoding='utf-8')
        
        # Create a README for downloads  
        downloads_readme = base_path / 'downloads' / 'README.txt'
        downloads_readme.write_text("📥 Download files from here!\n\nThis folder contains files ready for download.", encoding='utf-8')
        
        # Create a README for shared
        shared_readme = base_path / 'shared' / 'README.txt'
        shared_readme.write_text("🤝 Shared files folder!\n\nThis folder is for files shared between users.", encoding='utf-8')
        
        return True
    except Exception as e:
        print_warning(f"Could not create sample files: {e}")
        return False

def create_config(share_path, http_port=8000, ftp_port=21):
    """Create RoraFTP configuration."""
    config = {
        'share_path': str(share_path),
        'http_port': http_port,
        'ftp_port': ftp_port,
        'local_ip': get_local_ip(),
        'max_file_size': 10 * 1024 * 1024 * 1024,  # 10GB
        'enable_qr': True,
        'auto_open_browser': True,
        'version': '2.0.0',
        'deployment_time': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        config_file = Path('roraftp_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        print_success(f"Configuration saved to {config_file}")
        return config
    except Exception as e:
        print_error(f"Failed to create configuration: {e}")
        return None

def install_dependencies():
    """Install optional dependencies."""
    print_info("Installing optional dependencies for enhanced features...")
    
    dependencies = [
        ('pyftpdlib', 'FTP server support'),
        ('qrcode[pil]', 'QR code generation')
    ]
    
    for package, description in dependencies:
        try:
            print_info(f"Installing {package} ({description})...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print_success(f"Installed {package}")
        except subprocess.CalledProcessError:
            print_warning(f"Could not install {package} - {description} will be limited")
        except Exception as e:
            print_warning(f"Error installing {package}: {e}")

def get_user_preferences():
    """Get user preferences for deployment."""
    print(f"\n{Colors.BOLD}🔧 Deployment Configuration{Colors.END}")
    print("Press Enter to use default values in brackets")
    
    # Get share folder path
    default_path = Path.home() / 'RoraFTP_Files'
    share_path_input = input(f"\n📁 Share folder path [{default_path}]: ").strip()
    share_path = Path(share_path_input) if share_path_input else default_path
    
    # Get HTTP port
    http_port_input = input(f"🌐 HTTP port [8000]: ").strip()
    try:
        http_port = int(http_port_input) if http_port_input else 8000
    except ValueError:
        print_warning("Invalid port, using default 8000")
        http_port = 8000
    
    # Get FTP port
    ftp_port_input = input(f"📡 FTP port [21]: ").strip()
    try:
        ftp_port = int(ftp_port_input) if ftp_port_input else 21
    except ValueError:
        print_warning("Invalid port, using default 21")
        ftp_port = 21
    
    # Auto-open browser
    browser_input = input(f"🌍 Auto-open browser? [Y/n]: ").strip().lower()
    auto_browser = browser_input != 'n'
    
    return {
        'share_path': share_path,
        'http_port': http_port,
        'ftp_port': ftp_port,
        'auto_browser': auto_browser
    }

def main():
    """Main deployment function."""
    print_banner()
    
    print(f"{Colors.BOLD}🚀 Starting RoraFTP Deployment...{Colors.END}\n")
    
    # Step 1: Check requirements
    print_step(1, 7, "Checking system requirements...")
    if not check_python_version() or not check_roraftp_exists():
        sys.exit(1)
    
    # Step 2: Get user preferences
    print_step(2, 7, "Getting deployment preferences...")
    prefs = get_user_preferences()
    
    # Step 3: Create directories
    print_step(3, 7, f"Creating directory structure at {prefs['share_path']}...")
    created_dirs = create_directories(prefs['share_path'])
    if not created_dirs:
        print_error("Failed to create directories!")
        sys.exit(1)
    
    # Step 4: Create sample files
    print_step(4, 7, "Creating sample files...")
    create_sample_files(prefs['share_path'])
    
    # Step 5: Install dependencies
    print_step(5, 7, "Installing dependencies...")
    install_dependencies()
    
    # Step 6: Create configuration
    print_step(6, 7, "Creating configuration...")
    config = create_config(prefs['share_path'], prefs['http_port'], prefs['ftp_port'])
    if not config:
        print_error("Failed to create configuration!")
        sys.exit(1)
    
    # Step 7: Launch RoraFTP
    print_step(7, 7, "Launching RoraFTP...")
    
    print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 RoraFTP Deployment Complete!{Colors.END}\n")
    
    # Display connection info
    local_ip = config['local_ip']
    print(f"{Colors.BOLD}📡 Connection Information:{Colors.END}")
    print(f"   🌐 HTTP: http://{local_ip}:{prefs['http_port']}")
    print(f"   📡 FTP:  ftp://{local_ip}:{prefs['ftp_port']}")
    print(f"   📁 Files: {prefs['share_path']}")
    
    print(f"\n{Colors.BOLD}📱 Mobile Access:{Colors.END}")
    print(f"   • Open http://{local_ip}:{prefs['http_port']} in any mobile browser")
    print(f"   • iOS Files app: ftp://{local_ip}:{prefs['ftp_port']}")
    
    # Launch command
    launch_args = [
        sys.executable, 'quick_ftp_server.py',
        '--port', str(prefs['http_port']),
        '--ftp-port', str(prefs['ftp_port']),
        '--share', str(prefs['share_path'])
    ]
    
    if not prefs['auto_browser']:
        launch_args.append('--no-browser')
    
    print(f"\n{Colors.YELLOW}🚀 Starting RoraFTP server...{Colors.END}")
    print(f"{Colors.CYAN}Press Ctrl+C to stop the server{Colors.END}\n")
    
    try:
        subprocess.run(launch_args)
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}🛑 RoraFTP server stopped{Colors.END}")
    except Exception as e:
        print_error(f"Failed to start RoraFTP: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
