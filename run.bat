@echo off
REM RoraFTP - Universal File Server
REM Quick start script for Windows

setlocal enabledelayedexpansion

REM Colors (limited in Windows CMD)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Banner
echo.
echo %BLUE%🚀 RoraFTP - Universal File Server%NC%
echo ==================================================

REM Check Python
echo %BLUE%ℹ️  Checking Python installation...%NC%
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Python not found. Please install Python 3.6 or higher.%NC%
    echo %BLUE%ℹ️  Download from: https://www.python.org/downloads/%NC%
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Python %PYTHON_VERSION% found%NC%

REM Check if server file exists
if not exist "quick_ftp_server.py" (
    echo %RED%❌ quick_ftp_server.py not found in current directory%NC%
    echo %BLUE%ℹ️  Please run this script from the RoraFTP directory%NC%
    pause
    exit /b 1
)

REM Parse command line arguments
set "ARGS="
set "PORT="
set "FTP_PORT="
set "SHARE_PATH="
set "CREATE_VENV=false"

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--venv" set "CREATE_VENV=true" & shift & goto :parse_args
if "%~1"=="-v" set "CREATE_VENV=true" & shift & goto :parse_args
if "%~1"=="--port" set "PORT=%~2" & set "ARGS=%ARGS% --port %~2" & shift & shift & goto :parse_args
if "%~1"=="--ftp-port" set "FTP_PORT=%~2" & set "ARGS=%ARGS% --ftp-port %~2" & shift & shift & goto :parse_args
if "%~1"=="--share" set "SHARE_PATH=%~2" & set "ARGS=%ARGS% --share %~2" & shift & shift & goto :parse_args
if "%~1"=="--no-ftp" set "ARGS=%ARGS% --no-ftp" & shift & goto :parse_args
if "%~1"=="--no-qr" set "ARGS=%ARGS% --no-qr" & shift & goto :parse_args
if "%~1"=="--no-browser" set "ARGS=%ARGS% --no-browser" & shift & goto :parse_args
if "%~1"=="--help" goto :show_help
if "%~1"=="-h" goto :show_help
echo %YELLOW%⚠️  Unknown option: %~1%NC%
shift
goto :parse_args

:show_help
echo RoraFTP Quick Start Script
echo.
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo   --venv, -v          Create and use virtual environment
echo   --port PORT         HTTP server port (default: 8000)
echo   --ftp-port PORT     FTP server port (default: 21)
echo   --share PATH        Share folder path
echo   --no-ftp            Disable FTP server
echo   --no-qr             Disable QR code generation
echo   --no-browser        Don't auto-open browser
echo   --help, -h          Show this help message
echo.
echo Examples:
echo   %~nx0                          # Start with defaults
echo   %~nx0 --venv                   # Use virtual environment
echo   %~nx0 --port 9000              # Custom HTTP port
echo   %~nx0 --share C:\temp\files    # Custom share folder
echo   %~nx0 --no-ftp --no-browser    # HTTP only, no browser
echo.
pause
exit /b 0

:args_done

REM Create virtual environment if requested
if "%CREATE_VENV%"=="true" (
    echo %BLUE%ℹ️  Creating virtual environment...%NC%
    if not exist "venv" (
        python -m venv venv
        echo %GREEN%✅ Virtual environment created%NC%
    ) else (
        echo %YELLOW%⚠️  Virtual environment already exists%NC%
    )
    
    echo %BLUE%ℹ️  Activating virtual environment...%NC%
    call venv\Scripts\activate.bat
    echo %GREEN%✅ Virtual environment activated%NC%
    
    REM Install dependencies
    if exist "requirements.txt" (
        echo %BLUE%ℹ️  Installing dependencies...%NC%
        pip install -r requirements.txt
        echo %GREEN%✅ Dependencies installed%NC%
    )
)

REM Display configuration
echo.
echo %BLUE%ℹ️  Configuration:%NC%
if "%PORT%"=="" (
    echo   HTTP Port: 8000 (default)
) else (
    echo   HTTP Port: %PORT%
)
if "%FTP_PORT%"=="" (
    echo   FTP Port: 21 (default)
) else (
    echo   FTP Port: %FTP_PORT%
)
if "%SHARE_PATH%"=="" (
    echo   Share Path: %%USERPROFILE%%\FileShare (default)
) else (
    echo   Share Path: %SHARE_PATH%
)

REM Check for port conflicts (basic check)
if not "%PORT%"=="" (
    netstat -an | findstr ":%PORT% " >nul 2>&1
    if not errorlevel 1 (
        echo %YELLOW%⚠️  Port %PORT% appears to be in use%NC%
    )
)

REM Create share directory if specified
if not "%SHARE_PATH%"=="" (
    if not exist "%SHARE_PATH%" (
        echo %BLUE%ℹ️  Creating share directory: %SHARE_PATH%%NC%
        mkdir "%SHARE_PATH%" 2>nul
        if errorlevel 1 (
            echo %RED%❌ Failed to create share directory%NC%
            pause
            exit /b 1
        )
        echo %GREEN%✅ Share directory created%NC%
    )
)

REM Final confirmation
echo.
echo %BLUE%ℹ️  Starting RoraFTP server...%NC%
echo %YELLOW%⚠️  Press Ctrl+C to stop the server%NC%
echo.

REM Start the server
python quick_ftp_server.py %ARGS%

REM Pause on exit (in case of error)
if errorlevel 1 (
    echo.
    echo %RED%❌ Server exited with error%NC%
    pause
)
