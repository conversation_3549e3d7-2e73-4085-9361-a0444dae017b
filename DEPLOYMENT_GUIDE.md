# 🚀 RoraFTP Deployment Guide

Complete guide for deploying RoraFTP Cloud File Server with one-click setup.

## 📋 Quick Overview

RoraFTP v2.0 includes **automated deployment scripts** that handle everything:
- ✅ Directory creation
- ✅ Configuration setup  
- ✅ Dependency installation
- ✅ Sample file creation
- ✅ Server launch

## 🚀 One-Click Deployment Options

### Option 1: Windows Users (Easiest)
```cmd
# Double-click this file or run in Command Prompt
deploy_roraftp.bat
```

### Option 2: Linux/Mac Users
```bash
# Make executable and run
chmod +x deploy_roraftp.sh
./deploy_roraftp.sh
```

### Option 3: Python Script (All Platforms)
```bash
# Run the deployment script directly
python deploy_roraftp.py
```

### Option 4: Quick Start (Existing Users)
```bash
# Launch with defaults instantly
python start_roraftp.py
```

## 📁 What Gets Created

The deployment script automatically creates:

```
📁 RoraFTP_Files/                    # Main share directory
├── 📄 Welcome_to_RoraFTP.txt       # Welcome file with info
├── 📁 uploads/                     # Upload folder
│   └── 📄 README.txt
├── 📁 downloads/                   # Download folder  
│   └── 📄 README.txt
├── 📁 shared/                      # Shared files folder
│   └── 📄 README.txt
└── 📁 logs/                        # Log files (if enabled)
```

## ⚙️ Configuration Options

During deployment, you can customize:

| Setting | Default | Description |
|---------|---------|-------------|
| **Share Path** | `~/RoraFTP_Files` | Where files are stored |
| **HTTP Port** | `8000` | Web interface port |
| **FTP Port** | `21` | FTP server port |
| **Auto Browser** | `Yes` | Open browser automatically |

## 🌐 Access Methods

After deployment, access RoraFTP via:

### 📱 Mobile Devices
- **iPhone Safari**: `http://your-ip:8000` (drag & drop supported)
- **Android Chrome**: `http://your-ip:8000` (full compatibility)
- **iOS Files App**: `ftp://your-ip:21` (direct FTP access)

### 💻 Desktop
- **Web Browser**: `http://your-ip:8000` (all modern browsers)
- **FTP Client**: `ftp://your-ip:21` (FileZilla, WinSCP, etc.)

### 📱 QR Code Access
The deployment automatically generates a QR code for instant mobile access!

## 🔧 Advanced Configuration

### Environment Variables
Create a `.env` file for advanced settings:

```bash
# Performance settings
RORAFTP_MAX_FILE_SIZE=10737418240  # 10GB
RORAFTP_CHUNK_SIZE=1048576         # 1MB chunks
RORAFTP_MAX_CONNECTIONS=50

# Network settings  
RORAFTP_PORT=8000
RORAFTP_FTP_PORT=21

# Features
RORAFTP_ENABLE_QR=true
RORAFTP_AUTO_OPEN_BROWSER=true
```

### Manual Configuration
```bash
# Custom settings
python quick_ftp_server.py \
  --port 8080 \
  --ftp-port 2121 \
  --share /custom/path \
  --no-browser \
  --no-qr
```

## 🐳 Docker Deployment

### Quick Docker Run
```bash
# Pull and run
docker run -p 8000:8000 -p 21:21 -v ./files:/app/files roraftp:latest

# Or build locally
docker build -t roraftp .
docker run -p 8000:8000 -p 21:21 -v ./files:/app/files roraftp
```

### Docker Compose
```bash
# Start with docker-compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## 🔧 Systemd Service (Linux)

### Install as System Service
```bash
# Copy service file
sudo cp roraftp.service /etc/systemd/system/

# Enable and start
sudo systemctl enable roraftp
sudo systemctl start roraftp

# Check status
sudo systemctl status roraftp
```

## 🚨 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Check what's using the port
netstat -tulpn | grep :8000

# Use different port
python quick_ftp_server.py --port 8080
```

**Permission Denied (Linux/Mac)**
```bash
# Make scripts executable
chmod +x deploy_roraftp.sh
chmod +x start_roraftp.py

# Run with sudo for port 21 (FTP)
sudo python quick_ftp_server.py --ftp-port 21
```

**Python Not Found**
```bash
# Install Python 3.6+
# Windows: Download from python.org
# Ubuntu: sudo apt install python3
# macOS: brew install python3
```

**Dependencies Missing**
```bash
# Install manually
pip install pyftpdlib qrcode[pil]

# Or use deployment script (auto-installs)
python deploy_roraftp.py
```

### Performance Issues

**Slow Uploads**
- Check network speed
- Increase chunk size: `RORAFTP_CHUNK_SIZE=2097152` (2MB)
- Reduce concurrent connections if needed

**High Memory Usage**
- Reduce max file size: `RORAFTP_MAX_FILE_SIZE=1073741824` (1GB)
- Lower connection limit: `RORAFTP_MAX_CONNECTIONS=25`

## 📊 Performance Features

RoraFTP v2.0 includes these optimizations:

- ⚡ **Dynamic Chunk Sizing**: 64KB → 4MB adaptive
- 🔗 **HTTP Keep-Alive**: 20% faster requests
- 💾 **Smart Caching**: Intelligent cache headers
- 📊 **Range Requests**: Resume downloads
- 🗂️ **Metadata Caching**: 80% faster directory ops
- 🔌 **Socket Optimization**: TCP_NODELAY + 1MB buffers

## 🎯 Production Deployment

### Security Checklist
- [ ] Change default ports if needed
- [ ] Set appropriate file size limits
- [ ] Configure rate limiting
- [ ] Use HTTPS proxy (nginx/Apache) for production
- [ ] Set up firewall rules
- [ ] Regular security updates

### Monitoring
- [ ] Check logs regularly: `tail -f logs/roraftp.log`
- [ ] Monitor disk space in share directory
- [ ] Set up health check: `curl http://localhost:8000/health`

## 📞 Support

- **Documentation**: README.md
- **Issues**: GitHub Issues
- **Performance**: See optimization guide in README
- **Security**: Follow production security checklist

---

🚀 **RoraFTP v2.0** - Production-Ready Cloud File Server with Beautiful UI and High Performance!
