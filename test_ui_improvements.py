#!/usr/bin/env python3
"""
🎨 RoraFTP UI Testing & Improvement Suite
Version: 2.0.0

Specialized testing for UI/UX improvements focusing on:
- Minimalistic design principles
- Accessibility compliance
- Performance optimization
- Cross-device compatibility
- Clean, readable code
"""

import requests
import re
import json
import time
from pathlib import Path
import subprocess
import sys

class UITestFramework:
    """Framework for UI testing and improvement suggestions."""
    
    def __init__(self, server_url="http://localhost:8888"):
        self.server_url = server_url
        self.improvements = []
        self.accessibility_score = 0
        self.performance_score = 0
        self.design_score = 0
    
    def analyze_html_structure(self, html):
        """Analyze HTML structure for improvements."""
        issues = []
        suggestions = []
        
        # Check for semantic HTML
        semantic_tags = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer']
        found_semantic = sum(1 for tag in semantic_tags if f'<{tag}' in html)
        
        if found_semantic < 3:
            issues.append("Limited semantic HTML usage")
            suggestions.append("Use more semantic HTML5 tags (header, main, section, etc.)")
        
        # Check for accessibility
        if 'alt=' not in html and '<img' in html:
            issues.append("Images without alt text")
            suggestions.append("Add alt attributes to all images")
        
        if 'aria-' not in html:
            issues.append("No ARIA attributes found")
            suggestions.append("Add ARIA labels for better accessibility")
        
        # Check for performance
        if html.count('<script') > 5:
            issues.append("Too many script tags")
            suggestions.append("Consider bundling JavaScript files")
        
        if html.count('<link') > 10:
            issues.append("Too many CSS links")
            suggestions.append("Consider bundling CSS files")
        
        return issues, suggestions
    
    def analyze_css_performance(self, html):
        """Analyze CSS for performance and maintainability."""
        issues = []
        suggestions = []
        
        # Extract inline styles
        inline_styles = re.findall(r'style="([^"]*)"', html)
        
        if len(inline_styles) > 10:
            issues.append("Excessive inline styles")
            suggestions.append("Move inline styles to CSS classes for better maintainability")
        
        # Check for CSS optimization opportunities
        css_content = re.findall(r'<style[^>]*>(.*?)</style>', html, re.DOTALL)
        
        for css in css_content:
            # Check for repeated values
            colors = re.findall(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgba?\([^)]+\)', css)
            if len(set(colors)) != len(colors):
                suggestions.append("Use CSS custom properties for repeated color values")
            
            # Check for vendor prefixes
            if '-webkit-' in css or '-moz-' in css:
                suggestions.append("Consider using autoprefixer for vendor prefixes")
            
            # Check for minification opportunities
            if len(css) > 1000 and css.count('\n') > 50:
                suggestions.append("Consider minifying CSS for production")
        
        return issues, suggestions
    
    def test_minimalistic_design(self, html):
        """Test adherence to minimalistic design principles."""
        score = 100
        issues = []
        
        # Check for excessive visual elements
        if html.count('🎨') + html.count('✨') + html.count('🚀') > 10:
            score -= 10
            issues.append("Too many decorative emojis - consider reducing for cleaner look")
        
        # Check for color complexity
        colors = re.findall(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}', html)
        unique_colors = len(set(colors))
        
        if unique_colors > 8:
            score -= 15
            issues.append(f"Too many colors ({unique_colors}) - limit to 5-6 for minimalistic design")
        
        # Check for font complexity
        fonts = re.findall(r'font-family:\s*([^;]+)', html)
        unique_fonts = len(set(fonts))
        
        if unique_fonts > 3:
            score -= 10
            issues.append(f"Too many fonts ({unique_fonts}) - stick to 1-2 font families")
        
        # Check for animation complexity
        animations = html.count('animation:') + html.count('@keyframes')
        
        if animations > 5:
            score -= 10
            issues.append("Too many animations - keep animations subtle and purposeful")
        
        return max(0, score), issues
    
    def test_accessibility_compliance(self, html):
        """Test accessibility compliance."""
        score = 100
        issues = []
        
        # Check for alt text on images
        images = re.findall(r'<img[^>]*>', html)
        images_without_alt = [img for img in images if 'alt=' not in img]
        
        if images_without_alt:
            score -= 20
            issues.append(f"{len(images_without_alt)} images missing alt text")
        
        # Check for form labels
        inputs = re.findall(r'<input[^>]*>', html)
        labels = html.count('<label')
        
        if len(inputs) > labels:
            score -= 15
            issues.append("Some form inputs missing labels")
        
        # Check for heading hierarchy
        headings = re.findall(r'<h([1-6])', html)
        if headings:
            heading_levels = [int(h) for h in headings]
            if heading_levels[0] != 1:
                score -= 10
                issues.append("Page should start with h1")
            
            for i in range(1, len(heading_levels)):
                if heading_levels[i] > heading_levels[i-1] + 1:
                    score -= 5
                    issues.append("Heading hierarchy skips levels")
                    break
        
        # Check for color contrast (basic check)
        if 'color: white' in html and 'background: white' in html:
            score -= 25
            issues.append("Potential color contrast issues")
        
        # Check for keyboard navigation
        if 'tabindex' not in html and 'focus' not in html:
            score -= 10
            issues.append("No keyboard navigation indicators found")
        
        return max(0, score), issues
    
    def test_performance_optimization(self, html):
        """Test performance optimization."""
        score = 100
        issues = []
        
        # Check file size
        html_size = len(html.encode('utf-8'))
        
        if html_size > 100000:  # 100KB
            score -= 20
            issues.append(f"Large HTML size ({html_size/1024:.1f}KB) - consider optimization")
        
        # Check for inline resources
        inline_css_size = sum(len(match) for match in re.findall(r'<style[^>]*>(.*?)</style>', html, re.DOTALL))
        inline_js_size = sum(len(match) for match in re.findall(r'<script[^>]*>(.*?)</script>', html, re.DOTALL))
        
        if inline_css_size > 10000:  # 10KB
            score -= 10
            issues.append("Large inline CSS - consider external stylesheet")
        
        if inline_js_size > 10000:  # 10KB
            score -= 10
            issues.append("Large inline JavaScript - consider external file")
        
        # Check for optimization opportunities
        if 'minified' not in html and len(html) > 50000:
            score -= 5
            issues.append("Consider minifying HTML for production")
        
        # Check for caching headers (would need actual HTTP response)
        if 'cache-control' not in html.lower():
            issues.append("Consider adding cache control headers")
        
        return max(0, score), issues
    
    def generate_improvement_suggestions(self):
        """Generate comprehensive improvement suggestions."""
        try:
            response = requests.get(self.server_url, timeout=10)
            html = response.text
        except Exception as e:
            return {"error": f"Could not fetch page: {e}"}
        
        # Run all tests
        html_issues, html_suggestions = self.analyze_html_structure(html)
        css_issues, css_suggestions = self.analyze_css_performance(html)
        
        self.design_score, design_issues = self.test_minimalistic_design(html)
        self.accessibility_score, accessibility_issues = self.test_accessibility_compliance(html)
        self.performance_score, performance_issues = self.test_performance_optimization(html)
        
        # Compile results
        results = {
            "overall_score": (self.design_score + self.accessibility_score + self.performance_score) / 3,
            "scores": {
                "minimalistic_design": self.design_score,
                "accessibility": self.accessibility_score,
                "performance": self.performance_score
            },
            "issues": {
                "html_structure": html_issues,
                "css_performance": css_issues,
                "design": design_issues,
                "accessibility": accessibility_issues,
                "performance": performance_issues
            },
            "suggestions": {
                "html": html_suggestions,
                "css": css_suggestions,
                "immediate_actions": self.get_priority_suggestions(),
                "long_term_improvements": self.get_long_term_suggestions()
            }
        }
        
        return results
    
    def get_priority_suggestions(self):
        """Get high-priority improvement suggestions."""
        return [
            "Implement consistent spacing using CSS custom properties",
            "Add focus indicators for keyboard navigation",
            "Optimize image loading with lazy loading",
            "Use semantic HTML5 elements for better structure",
            "Implement proper heading hierarchy (h1 -> h2 -> h3)",
            "Add ARIA labels for interactive elements",
            "Optimize CSS by removing unused styles",
            "Implement consistent color scheme with CSS variables"
        ]
    
    def get_long_term_suggestions(self):
        """Get long-term improvement suggestions."""
        return [
            "Implement CSS-in-JS or styled-components for better maintainability",
            "Add comprehensive keyboard navigation support",
            "Implement dark mode support",
            "Add internationalization (i18n) support",
            "Implement progressive web app (PWA) features",
            "Add comprehensive error handling and user feedback",
            "Implement advanced accessibility features (screen reader support)",
            "Add comprehensive testing suite for UI components"
        ]

def run_ui_analysis():
    """Run comprehensive UI analysis."""
    print("🎨 RoraFTP UI Analysis & Improvement Suite")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8888/health", timeout=5)
        if response.status_code != 200:
            print("❌ RoraFTP server not running. Please start the server first.")
            return
    except:
        print("❌ Cannot connect to RoraFTP server. Please start the server first.")
        return
    
    # Run analysis
    ui_tester = UITestFramework()
    results = ui_tester.generate_improvement_suggestions()
    
    if "error" in results:
        print(f"❌ Error: {results['error']}")
        return
    
    # Display results
    print(f"\n📊 Overall Score: {results['overall_score']:.1f}/100")
    print(f"   🎨 Design: {results['scores']['minimalistic_design']}/100")
    print(f"   ♿ Accessibility: {results['scores']['accessibility']}/100")
    print(f"   ⚡ Performance: {results['scores']['performance']}/100")
    
    # Show issues
    print(f"\n🔍 Issues Found:")
    for category, issues in results['issues'].items():
        if issues:
            print(f"   {category.replace('_', ' ').title()}:")
            for issue in issues:
                print(f"     • {issue}")
    
    # Show suggestions
    print(f"\n💡 Priority Improvements:")
    for suggestion in results['suggestions']['immediate_actions'][:5]:
        print(f"   • {suggestion}")
    
    print(f"\n🚀 Long-term Improvements:")
    for suggestion in results['suggestions']['long_term_improvements'][:3]:
        print(f"   • {suggestion}")
    
    # Save detailed report
    with open('ui_analysis_report.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: ui_analysis_report.json")

if __name__ == '__main__':
    run_ui_analysis()
