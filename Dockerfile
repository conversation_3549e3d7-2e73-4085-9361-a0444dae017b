# RoraFTP - Universal File Server
# Multi-stage Docker build for production deployment

# Build stage
FROM python:3.9-slim as builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.9-slim

# Create non-root user for security
RUN groupadd -r roraftp && useradd -r -g roraftp roraftp

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application files
COPY quick_ftp_server.py .
COPY *.json ./

# Create directories
RUN mkdir -p /app/files /app/logs && \
    chown -R roraftp:roraftp /app

# Switch to non-root user
USER roraftp

# Add local Python packages to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Environment variables
ENV RORAFTP_PORT=8000
ENV RORAFTP_FTP_PORT=21
ENV RORAFTP_SHARE_PATH=/app/files
ENV RORAFTP_MAX_FILE_SIZE=104857600
ENV RORAFTP_RATE_LIMIT=50
ENV PYTHONUNBUFFERED=1

# Expose ports
EXPOSE 8000 21 21100-21199

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Volume for file storage
VOLUME ["/app/files"]

# Default command
CMD ["python", "quick_ftp_server.py", "--share", "/app/files", "--no-browser"]
