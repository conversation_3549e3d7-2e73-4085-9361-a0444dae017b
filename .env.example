# RoraFTP Environment Configuration
# Copy this file to .env and modify as needed

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# HTTP server port (default: 8000)
RORAFTP_PORT=8000

# FTP server port (default: 21)
# Note: Ports below 1024 require root privileges on Linux/Mac
RORAFTP_FTP_PORT=21

# Share folder path (default: ~/FileShare)
# Use absolute paths for production
RORAFTP_SHARE_PATH=/var/www/files

# Server IP address (auto-detected if not set)
# RORAFTP_LOCAL_IP=*************

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Maximum file size in bytes (default: 104857600 = 100MB)
RORAFTP_MAX_FILE_SIZE=104857600

# Rate limiting: requests per minute per IP (default: 50)
RORAFTP_RATE_LIMIT=50

# Maximum concurrent connections (default: 50)
RORAFTP_MAX_CONNECTIONS=50

# Rate limiting window in seconds (default: 60)
RORAFTP_RATE_LIMIT_WINDOW=60

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# File upload chunk size in bytes (default: 65536 = 64KB)
RORAFTP_CHUNK_SIZE=65536

# Enable gzip compression for responses (default: true)
RORAFTP_ENABLE_COMPRESSION=true

# =============================================================================
# FEATURE TOGGLES
# =============================================================================

# Enable QR code generation (default: true)
RORAFTP_ENABLE_QR=true

# Auto-open browser on startup (default: true)
RORAFTP_AUTO_OPEN_BROWSER=true

# Enable FTP server (default: true)
RORAFTP_ENABLE_FTP=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)
RORAFTP_LOG_LEVEL=INFO

# Log file path (default: fileserver.log)
RORAFTP_LOG_FILE=fileserver.log

# Security log file path (default: server_security.log)
RORAFTP_SECURITY_LOG_FILE=server_security.log

# Enable console logging (default: true)
RORAFTP_CONSOLE_LOGGING=true

# =============================================================================
# FTP SPECIFIC SETTINGS
# =============================================================================

# FTP passive mode port range start (default: 21100)
RORAFTP_FTP_PASSIVE_PORT_START=21100

# FTP passive mode port range end (default: 21199)
RORAFTP_FTP_PASSIVE_PORT_END=21199

# FTP masquerade address (for NAT/firewall, auto-detected if not set)
# RORAFTP_FTP_MASQUERADE_ADDRESS=your.public.ip.address

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug mode (default: false)
# WARNING: Do not enable in production
RORAFTP_DEBUG=false

# Enable development features (default: false)
RORAFTP_DEV_MODE=false

# =============================================================================
# DOCKER SPECIFIC SETTINGS
# =============================================================================

# Container timezone (default: UTC)
TZ=UTC

# Python unbuffered output (recommended for containers)
PYTHONUNBUFFERED=1

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Development setup:
# RORAFTP_PORT=8000
# RORAFTP_FTP_PORT=2121
# RORAFTP_SHARE_PATH=./dev_files
# RORAFTP_DEBUG=true
# RORAFTP_AUTO_OPEN_BROWSER=true

# Production setup:
# RORAFTP_PORT=8000
# RORAFTP_FTP_PORT=21
# RORAFTP_SHARE_PATH=/var/www/files
# RORAFTP_MAX_FILE_SIZE=524288000  # 500MB
# RORAFTP_RATE_LIMIT=100
# RORAFTP_MAX_CONNECTIONS=100
# RORAFTP_AUTO_OPEN_BROWSER=false
# RORAFTP_LOG_LEVEL=WARNING

# High-security setup:
# RORAFTP_MAX_FILE_SIZE=52428800   # 50MB
# RORAFTP_RATE_LIMIT=20
# RORAFTP_MAX_CONNECTIONS=25
# RORAFTP_ENABLE_FTP=false
# RORAFTP_LOG_LEVEL=INFO

# High-performance setup:
# RORAFTP_MAX_FILE_SIZE=1073741824  # 1GB
# RORAFTP_RATE_LIMIT=200
# RORAFTP_MAX_CONNECTIONS=200
# RORAFTP_CHUNK_SIZE=131072         # 128KB
# RORAFTP_ENABLE_COMPRESSION=true
