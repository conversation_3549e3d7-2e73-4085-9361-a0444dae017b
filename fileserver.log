2025-06-07 22:00:57,034 - INFO - Config saved to fileserver_config.json
2025-06-07 22:00:57,035 - INFO - Share folder ready: ./test_files
2025-06-07 22:00:57,036 - INFO - Starting servers...
2025-06-07 22:00:57,894 - INFO - HTTP server started on port 8888
2025-06-07 22:00:57,941 - INFO - FTP server started on port 2121
2025-06-07 22:00:57,941 - INFO - PRODUCTION-READY UNIVERSAL FILE SERVER
2025-06-07 22:00:57,942 - INFO - concurrency model: async
2025-06-07 22:00:57,942 - INFO - masquerade (NAT) address: ************
2025-06-07 22:00:57,942 - INFO - passive ports: 21100->21199
2025-06-07 22:00:57,942 - INFO - >>> starting FTP server on 0.0.0.0:2121, pid=5136 <<<
2025-06-07 22:02:17,045 - INFO - Config saved to fileserver_config.json
2025-06-07 22:02:17,046 - INFO - Share folder ready: ./test_files
2025-06-07 22:02:17,046 - INFO - Starting servers...
2025-06-07 22:02:17,903 - INFO - HTTP server started on port 8888
2025-06-07 22:02:17,924 - INFO - FTP server started on port 2121
2025-06-07 22:02:17,925 - INFO - PRODUCTION-READY UNIVERSAL FILE SERVER
2025-06-07 22:02:30,586 - INFO - Browser 127.0.0.1 - "GET /health HTTP/1.1" 200 -
2025-06-07 22:02:47,080 - INFO - Browser 127.0.0.1 - "GET /test.txt HTTP/1.1" 200 -
2025-06-07 22:02:51,684 - INFO - Browser 127.0.0.1 - "GET / HTTP/1.1" 200 -
2025-06-07 22:02:59,806 - INFO - Browser 127.0.0.1 - "GET / HTTP/1.1" 200 -
2025-06-07 22:03:26,235 - INFO - Config saved to fileserver_config.json
2025-06-07 22:03:26,236 - INFO - Share folder ready: ./test_files
2025-06-07 22:03:26,236 - INFO - Starting servers...
2025-06-07 22:03:27,086 - INFO - HTTP server started on port 8888
2025-06-07 22:03:27,107 - INFO - FTP server started on port 2121
2025-06-07 22:03:27,107 - INFO - PRODUCTION-READY UNIVERSAL FILE SERVER
2025-06-07 22:03:43,753 - INFO - Browser 127.0.0.1 - "GET / HTTP/1.1" 200 -
2025-06-07 22:03:51,245 - INFO - Browser 127.0.0.1 - "GET / HTTP/1.1" 200 -
