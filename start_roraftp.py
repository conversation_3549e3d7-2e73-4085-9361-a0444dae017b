#!/usr/bin/env python3
"""
🚀 RoraFTP Quick Start
One-click launcher for RoraFTP with sensible defaults.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Quick start RoraFTP with defaults."""
    print("🚀 RoraFTP Quick Start")
    print("=" * 30)
    
    # Check if main file exists
    if not Path('quick_ftp_server.py').exists():
        print("❌ RoraFTP not found! Please run deploy_roraftp.py first.")
        sys.exit(1)
    
    # Default settings
    share_path = Path.home() / 'RoraFTP_Files'
    
    # Create directory if it doesn't exist
    if not share_path.exists():
        print(f"📁 Creating share directory: {share_path}")
        share_path.mkdir(parents=True, exist_ok=True)
        
        # Create welcome file
        welcome = share_path / 'Welcome.txt'
        welcome.write_text("Welcome to RoraFTP!\nDrag and drop files here to upload.", encoding='utf-8')
    
    print(f"📁 Share folder: {share_path}")
    print(f"🌐 Starting on http://localhost:8000")
    print(f"📡 FTP on port 21")
    print("\nPress Ctrl+C to stop\n")
    
    # Launch RoraFTP
    try:
        subprocess.run([
            sys.executable, 'quick_ftp_server.py',
            '--share', str(share_path)
        ])
    except KeyboardInterrupt:
        print("\n🛑 RoraFTP stopped")

if __name__ == '__main__':
    main()
