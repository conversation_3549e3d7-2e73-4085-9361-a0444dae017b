# RoraFTP Deployment Guide

This guide covers various deployment scenarios for RoraFTP, from simple local setups to production environments.

## 🏠 Local Development

### Quick Start
```bash
# Clone and run
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP
python quick_ftp_server.py
```

### With Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python quick_ftp_server.py
```

## 🖥️ Desktop Deployment

### Windows
1. **Download Python 3.6+** from python.org
2. **Download RoraFTP** and extract
3. **Run** `quick_ftp_server.py`
4. **Firewall**: Allow Python through Windows Firewall

### macOS
```bash
# Install Python (if not already installed)
brew install python3

# Run RoraFTP
python3 quick_ftp_server.py
```

### Linux (Ubuntu/Debian)
```bash
# Install Python
sudo apt update
sudo apt install python3 python3-pip

# Run RoraFTP
python3 quick_ftp_server.py
```

## 🌐 Network Deployment

### Home Network
1. **Find your IP**: The server displays it automatically
2. **Port forwarding**: Forward ports 8000 and 21 on your router
3. **Firewall**: Allow ports through system firewall
4. **Access**: Use `http://YOUR_PUBLIC_IP:8000`

### Corporate Network
```bash
# Use custom ports to avoid conflicts
python quick_ftp_server.py --port 9000 --ftp-port 2121
```

## ☁️ Cloud Deployment

### VPS/Cloud Server
```bash
# Install on Ubuntu/CentOS
sudo apt update && sudo apt install python3 python3-pip
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP

# Run with custom configuration
python3 quick_ftp_server.py --port 8000 --share /var/www/files
```

### AWS EC2
1. **Launch EC2 instance** (Ubuntu 20.04 LTS)
2. **Security Group**: Allow ports 8000, 21, 21100-21199
3. **Install and run**:
```bash
sudo apt update
sudo apt install python3 python3-pip git
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP
python3 quick_ftp_server.py --share /home/<USER>/files
```

### Google Cloud Platform
```bash
# Create VM instance
gcloud compute instances create roraftp-server \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-micro

# SSH and setup
gcloud compute ssh roraftp-server
sudo apt update && sudo apt install python3 python3-pip git
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP
python3 quick_ftp_server.py
```

### DigitalOcean Droplet
1. **Create Droplet** (Ubuntu 20.04)
2. **Firewall**: Allow ports 8000, 21, 21100-21199
3. **Setup**:
```bash
apt update && apt install python3 python3-pip git
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP
python3 quick_ftp_server.py --share /var/www/files
```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt

EXPOSE 8000 21 21100-21199

CMD ["python", "quick_ftp_server.py", "--share", "/app/files"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  roraftp:
    build: .
    ports:
      - "8000:8000"
      - "21:21"
      - "21100-21199:21100-21199"
    volumes:
      - ./files:/app/files
    environment:
      - RORAFTP_PORT=8000
      - RORAFTP_FTP_PORT=21
```

### Build and Run
```bash
# Build image
docker build -t roraftp .

# Run container
docker run -p 8000:8000 -p 21:21 -v ./files:/app/files roraftp
```

## 🔧 Production Configuration

### Environment Variables
```bash
export RORAFTP_PORT=8000
export RORAFTP_FTP_PORT=21
export RORAFTP_SHARE_PATH=/var/www/files
export RORAFTP_MAX_FILE_SIZE=104857600
export RORAFTP_RATE_LIMIT=50
export RORAFTP_LOG_LEVEL=INFO
```

### Systemd Service (Linux)
Create `/etc/systemd/system/roraftp.service`:
```ini
[Unit]
Description=RoraFTP Universal File Server
After=network.target

[Service]
Type=simple
User=roraftp
WorkingDirectory=/opt/roraftp
ExecStart=/usr/bin/python3 quick_ftp_server.py --share /var/www/files
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable roraftp
sudo systemctl start roraftp
sudo systemctl status roraftp
```

### Nginx Reverse Proxy
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### SSL/HTTPS with Let's Encrypt
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔒 Security Considerations

### Firewall Configuration
```bash
# Ubuntu/Debian
sudo ufw allow 8000
sudo ufw allow 21
sudo ufw allow 21100:21199/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=21/tcp
sudo firewall-cmd --permanent --add-port=21100-21199/tcp
sudo firewall-cmd --reload
```

### File Permissions
```bash
# Create dedicated user
sudo useradd -r -s /bin/false roraftp
sudo mkdir -p /var/www/files
sudo chown roraftp:roraftp /var/www/files
sudo chmod 755 /var/www/files
```

### Rate Limiting
```bash
# Increase rate limit for production
python quick_ftp_server.py --rate-limit 100
```

## 📊 Monitoring

### Log Files
- `fileserver.log` - Application logs
- `server_security.log` - Security events

### Health Check
```bash
curl http://localhost:8000/health
```

### Process Monitoring
```bash
# Check if running
ps aux | grep quick_ftp_server

# Monitor resources
top -p $(pgrep -f quick_ftp_server)
```

## 🚨 Troubleshooting

### Common Issues

**Port conflicts:**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8000
sudo lsof -i :8000

# Use different port
python quick_ftp_server.py --port 9000
```

**Permission denied:**
```bash
# For ports < 1024, run as root or use higher ports
sudo python quick_ftp_server.py --ftp-port 2121
```

**FTP passive mode issues:**
```bash
# Configure passive port range
python quick_ftp_server.py --passive-ports 21100-21199
```

**Memory issues:**
```bash
# Reduce max file size
export RORAFTP_MAX_FILE_SIZE=52428800  # 50MB
```

## 📈 Performance Tuning

### High Traffic
```bash
# Increase connection limits
export RORAFTP_MAX_CONNECTIONS=100
export RORAFTP_RATE_LIMIT=200
```

### Large Files
```bash
# Increase chunk size for better performance
export RORAFTP_CHUNK_SIZE=131072  # 128KB
```

### Load Balancing
Use multiple instances behind a load balancer:
```bash
# Instance 1
python quick_ftp_server.py --port 8001

# Instance 2
python quick_ftp_server.py --port 8002
```

---

For more deployment scenarios and advanced configurations, see the [Wiki](https://github.com/ForgottenCNZ/RoraFTP/wiki).
