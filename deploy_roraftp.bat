@echo off
title RoraFTP Deployment
color 0B

echo.
echo ========================================
echo   🚀 RoraFTP Cloud File Server
echo   One-Click Windows Deployment
echo ========================================
echo.

echo [1/3] Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.6+ first.
    echo    Download from: https://python.org
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo [2/3] Installing dependencies...
python -m pip install pyftpdlib qrcode[pil] --quiet
echo ✅ Dependencies installed

echo.
echo [3/3] Launching RoraFTP deployment...
python deploy_roraftp.py

pause
