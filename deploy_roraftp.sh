#!/bin/bash

# RoraFTP Deployment Script for Linux/Mac
# One-click deployment for RoraFTP Cloud File Server

# Colors
BLUE='\033[0;34m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "  🚀 RoraFTP Cloud File Server"
echo "  One-Click Linux/Mac Deployment"
echo "========================================"
echo -e "${NC}"

# Check Python
echo -e "${BLUE}[1/3]${NC} Checking Python..."
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 not found!${NC}"
    echo "Please install Python 3.6+ first."
    exit 1
fi
echo -e "${GREEN}✅ Python 3 found${NC}"

# Install dependencies
echo -e "${BLUE}[2/3]${NC} Installing dependencies..."
python3 -m pip install pyftpdlib qrcode[pil] --quiet --user
echo -e "${GREEN}✅ Dependencies installed${NC}"

# Make scripts executable
chmod +x deploy_roraftp.py
chmod +x start_roraftp.py

# Launch deployment
echo -e "${BLUE}[3/3]${NC} Launching RoraFTP deployment..."
python3 deploy_roraftp.py
