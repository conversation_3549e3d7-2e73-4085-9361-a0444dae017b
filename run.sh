#!/bin/bash

# RoraFTP - Universal File Server
# Quick start script for Linux/macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_status "$GREEN" "✅ $1"
}

print_error() {
    print_status "$RED" "❌ $1"
}

print_warning() {
    print_status "$YELLOW" "⚠️  $1"
}

print_info() {
    print_status "$BLUE" "ℹ️  $1"
}

# Banner
echo ""
print_info "🚀 RoraFTP - Universal File Server"
echo "=================================================="

# Check Python version
print_info "Checking Python version..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    print_error "Python not found. Please install Python 3.6 or higher."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.6"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_error "Python $PYTHON_VERSION found, but Python $REQUIRED_VERSION or higher is required."
    exit 1
fi

print_success "Python $PYTHON_VERSION found"

# Check if virtual environment should be created
CREATE_VENV=false
if [ "$1" = "--venv" ] || [ "$1" = "-v" ]; then
    CREATE_VENV=true
    shift  # Remove the flag from arguments
fi

# Create virtual environment if requested
if [ "$CREATE_VENV" = true ]; then
    print_info "Creating virtual environment..."
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    print_info "Activating virtual environment..."
    source venv/bin/activate
    print_success "Virtual environment activated"
    
    # Install dependencies
    if [ -f "requirements.txt" ]; then
        print_info "Installing dependencies..."
        pip install -r requirements.txt
        print_success "Dependencies installed"
    fi
fi

# Check if server file exists
if [ ! -f "quick_ftp_server.py" ]; then
    print_error "quick_ftp_server.py not found in current directory"
    print_info "Please run this script from the RoraFTP directory"
    exit 1
fi

# Parse command line arguments
ARGS=""
PORT=""
FTP_PORT=""
SHARE_PATH=""
NO_FTP=false
NO_QR=false
NO_BROWSER=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --port)
            PORT="$2"
            ARGS="$ARGS --port $2"
            shift 2
            ;;
        --ftp-port)
            FTP_PORT="$2"
            ARGS="$ARGS --ftp-port $2"
            shift 2
            ;;
        --share)
            SHARE_PATH="$2"
            ARGS="$ARGS --share $2"
            shift 2
            ;;
        --no-ftp)
            NO_FTP=true
            ARGS="$ARGS --no-ftp"
            shift
            ;;
        --no-qr)
            NO_QR=true
            ARGS="$ARGS --no-qr"
            shift
            ;;
        --no-browser)
            NO_BROWSER=true
            ARGS="$ARGS --no-browser"
            shift
            ;;
        --help|-h)
            echo "RoraFTP Quick Start Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --venv, -v          Create and use virtual environment"
            echo "  --port PORT         HTTP server port (default: 8000)"
            echo "  --ftp-port PORT     FTP server port (default: 21)"
            echo "  --share PATH        Share folder path"
            echo "  --no-ftp            Disable FTP server"
            echo "  --no-qr             Disable QR code generation"
            echo "  --no-browser        Don't auto-open browser"
            echo "  --help, -h          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                          # Start with defaults"
            echo "  $0 --venv                   # Use virtual environment"
            echo "  $0 --port 9000              # Custom HTTP port"
            echo "  $0 --share /tmp/files       # Custom share folder"
            echo "  $0 --no-ftp --no-browser   # HTTP only, no browser"
            echo ""
            exit 0
            ;;
        *)
            print_warning "Unknown option: $1"
            shift
            ;;
    esac
done

# Display configuration
echo ""
print_info "Configuration:"
echo "  HTTP Port: ${PORT:-8000 (default)}"
echo "  FTP Port: ${FTP_PORT:-21 (default)}"
echo "  Share Path: ${SHARE_PATH:-~/FileShare (default)}"
echo "  FTP Enabled: $([ "$NO_FTP" = true ] && echo "No" || echo "Yes")"
echo "  QR Codes: $([ "$NO_QR" = true ] && echo "No" || echo "Yes")"
echo "  Auto Browser: $([ "$NO_BROWSER" = true ] && echo "No" || echo "Yes")"

# Check for port conflicts
if [ -n "$PORT" ]; then
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$PORT "; then
            print_warning "Port $PORT appears to be in use"
        fi
    fi
fi

if [ -n "$FTP_PORT" ] && [ "$NO_FTP" != true ]; then
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$FTP_PORT "; then
            print_warning "FTP port $FTP_PORT appears to be in use"
        fi
    fi
fi

# Check permissions for low ports
if [ -n "$FTP_PORT" ] && [ "$FTP_PORT" -lt 1024 ] && [ "$NO_FTP" != true ]; then
    if [ "$EUID" -ne 0 ]; then
        print_warning "FTP port $FTP_PORT requires root privileges"
        print_info "Consider using a higher port (e.g., --ftp-port 2121) or run with sudo"
    fi
fi

# Create share directory if specified
if [ -n "$SHARE_PATH" ]; then
    if [ ! -d "$SHARE_PATH" ]; then
        print_info "Creating share directory: $SHARE_PATH"
        mkdir -p "$SHARE_PATH" || {
            print_error "Failed to create share directory"
            exit 1
        }
        print_success "Share directory created"
    fi
fi

# Final confirmation
echo ""
print_info "Starting RoraFTP server..."
print_warning "Press Ctrl+C to stop the server"
echo ""

# Start the server
exec $PYTHON_CMD quick_ftp_server.py $ARGS
