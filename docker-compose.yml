version: '3.8'

services:
  roraftp:
    build: .
    container_name: roraftp-server
    restart: unless-stopped
    
    ports:
      # HTTP server
      - "8000:8000"
      # FTP server
      - "21:21"
      # FTP passive mode ports
      - "21100-21199:21100-21199"
    
    volumes:
      # File storage
      - ./files:/app/files
      # Logs
      - ./logs:/app/logs
      # Configuration (optional)
      - ./config:/app/config
    
    environment:
      # Server configuration
      - RORAFTP_PORT=8000
      - RORAFTP_FTP_PORT=21
      - RORAFTP_SHARE_PATH=/app/files
      
      # Security settings
      - RORAFTP_MAX_FILE_SIZE=104857600  # 100MB
      - RORAFTP_RATE_LIMIT=50
      - RORAFTP_MAX_CONNECTIONS=50
      
      # Performance settings
      - RORAFTP_CHUNK_SIZE=65536  # 64KB
      
      # Feature toggles
      - RORAFTP_ENABLE_QR=true
      - RORAFTP_AUTO_OPEN_BROWSER=false
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Resource limits (optional)
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Nginx reverse proxy for HTTPS
  nginx:
    image: nginx:alpine
    container_name: roraftp-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    
    depends_on:
      - roraftp
    
    # Uncomment to enable
    profiles:
      - nginx

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: roraftp-prometheus
    restart: unless-stopped
    
    ports:
      - "9090:9090"
    
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    
    # Uncomment to enable
    profiles:
      - monitoring

volumes:
  prometheus_data:

networks:
  default:
    name: roraftp-network
