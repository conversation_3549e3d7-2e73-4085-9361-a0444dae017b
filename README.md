# RoraFTP - Universal File Server

[![Python 3.6+](https://img.shields.io/badge/python-3.6+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Production Ready](https://img.shields.io/badge/status-production%20ready-green.svg)](https://github.com/ForgottenCNZ/RoraFTP)

A production-ready, universal file server that works seamlessly across all devices and platforms. Built with simplicity, security, and compatibility in mind.

## ✨ Features

### 🌐 Universal Compatibility
- **📱 iPhone Safari**: Full drag-and-drop support
- **🤖 Android Chrome**: Native file upload/download
- **💻 Desktop Browsers**: All modern browsers supported
- **📁 iOS Files App**: Direct FTP integration
- **🔧 FTP Clients**: FileZilla, WinSCP, and more

### 🚀 Production Features
- **Multi-threaded**: Supports up to 50 concurrent connections
- **Rate Limiting**: Configurable request throttling
- **File Validation**: Secure file type and size checking
- **Streaming Uploads**: Memory-efficient large file handling
- **QR Code Access**: Instant mobile connection
- **Auto-Discovery**: Automatic network configuration

### 🔒 Security
- File type whitelist with safe extensions only
- Rate limiting to prevent abuse
- Filename sanitization
- Size limits (configurable, default 100MB)
- No arbitrary code execution

### 📱 Mobile-First Design
- Touch-friendly interface
- Responsive design for all screen sizes
- Drag-and-drop file uploads
- Progress indicators
- Offline-capable design

## 🚀 Quick Start

### Option 1: Direct Run (Recommended)
```bash
# Clone the repository
git clone https://github.com/ForgottenCNZ/RoraFTP.git
cd RoraFTP

# Run the server (auto-installs dependencies)
python quick_ftp_server.py
```

### Option 2: With Virtual Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the server
python quick_ftp_server.py
```

### Option 3: Docker (Coming Soon)
```bash
docker run -p 8000:8000 -p 21:21 -v ./files:/app/files roraftp:latest
```

## 📖 Usage

### Basic Usage
```bash
# Start with default settings (HTTP: 8000, FTP: 21)
python quick_ftp_server.py

# Custom ports
python quick_ftp_server.py --port 9000 --ftp-port 2121

# Custom share folder
python quick_ftp_server.py --share /path/to/files

# HTTP only (no FTP)
python quick_ftp_server.py --no-ftp

# Disable QR codes
python quick_ftp_server.py --no-qr

# Don't auto-open browser
python quick_ftp_server.py --no-browser
```

### Mobile Access
1. **Web Browser**: Open `http://YOUR_IP:8000` on any device
2. **QR Code**: Scan the displayed QR code with your phone camera
3. **iOS Files App**: Add server with `ftp://YOUR_IP:21`

### API Endpoints
- `GET /` - Web interface and file listing
- `POST /upload` - File upload endpoint
- `GET /health` - Health check endpoint
- `GET /filename` - Download specific file

## ⚙️ Configuration

### Environment Variables
```bash
export RORAFTP_PORT=8000
export RORAFTP_FTP_PORT=21
export RORAFTP_SHARE_PATH=/path/to/files
export RORAFTP_MAX_FILE_SIZE=104857600  # 100MB
export RORAFTP_RATE_LIMIT=50
```

### Configuration File
The server automatically creates `fileserver_config.json`:
```json
{
  "share_path": "/home/<USER>/FileShare",
  "http_port": 8000,
  "ftp_port": 21,
  "local_ip": "*************",
  "max_file_size": 104857600,
  "enable_qr": true,
  "auto_open_browser": true
}
```

## 🛠️ Development

### Requirements
- Python 3.6 or higher
- Optional: `qrcode[pil]` for QR code generation
- Optional: `pyftpdlib` for FTP server

### Dependencies
All dependencies are automatically installed when needed:
- `qrcode[pil]` - QR code generation
- `pyftpdlib` - FTP server functionality

### File Structure
```
RoraFTP/
├── quick_ftp_server.py          # Main server application
├── requirements.txt             # Python dependencies
├── README.md                   # This file
├── DEPLOYMENT.md               # Deployment guide
├── simple_server_config.json   # Basic configuration
├── optimized_server_config.json # Advanced configuration
└── server_security.log         # Security audit log
```

## 📋 Supported File Types (Universal Support)

### Documents & Text
- **Text Files**: `.txt`, `.rtf`, `.md`, `.markdown`, `.rst`, `.tex`, `.log`
- **Office Documents**: `.pdf`, `.doc`, `.docx`, `.odt`, `.pages`
- **Spreadsheets**: `.xls`, `.xlsx`, `.ods`, `.numbers`, `.xlsm`, `.xlsb`
- **Presentations**: `.ppt`, `.pptx`, `.odp`, `.key`, `.pps`, `.ppsx`
- **E-books**: `.epub`, `.mobi`, `.azw`, `.azw3`, `.fb2`, `.lit`

### Images & Graphics
- **Common Images**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.tiff`, `.webp`
- **Vector Graphics**: `.svg`, `.ai`, `.eps`
- **Professional**: `.psd`, `.raw`, `.cr2`, `.nef`, `.arw`, `.dng`
- **Modern Formats**: `.heic`, `.heif`, `.avif`
- **Icons**: `.ico`

### Audio & Video
- **Audio**: `.mp3`, `.wav`, `.flac`, `.aac`, `.ogg`, `.wma`, `.m4a`, `.opus`
- **Video**: `.mp4`, `.avi`, `.mov`, `.mkv`, `.wmv`, `.flv`, `.webm`, `.m4v`
- **Streaming**: `.3gp`, `.ogv`, `.ts`, `.mts`

### Programming & Development
- **Languages**: `.py`, `.js`, `.html`, `.css`, `.php`, `.java`, `.cpp`, `.c`, `.cs`
- **Modern Languages**: `.go`, `.rs`, `.swift`, `.kt`, `.scala`, `.rb`
- **Scripts**: `.sh`, `.bat`, `.ps1`, `.pl`
- **Data**: `.sql`, `.r`, `.m`, `.vb`, `.pas`, `.asm`

### Data & Configuration
- **Structured Data**: `.json`, `.xml`, `.yaml`, `.yml`, `.toml`, `.csv`, `.tsv`
- **Configuration**: `.ini`, `.cfg`, `.conf`, `.properties`, `.env`
- **Databases**: `.db`, `.sqlite`, `.mdb`
- **Scientific**: `.mat`, `.hdf5`, `.nc`, `.fits`, `.nii`, `.dcm`

### Archives & Compression
- **Common**: `.zip`, `.rar`, `.7z`, `.tar`, `.gz`, `.bz2`, `.xz`
- **System Images**: `.dmg`, `.iso`

### Web & Internet
- **Web Pages**: `.htm`, `.xhtml`, `.jsp`, `.asp`, `.aspx`
- **Templates**: `.erb`, `.ejs`, `.handlebars`, `.mustache`, `.twig`

### Specialized Formats
- **Fonts**: `.ttf`, `.otf`, `.woff`, `.woff2`, `.eot`
- **3D & CAD**: `.obj`, `.fbx`, `.dae`, `.3ds`, `.blend`, `.dwg`, `.dxf`
- **Academic**: `.bib`, `.ris`, `.endnote`
- **System**: `.lnk`, `.url`, `.webloc`, `.torrent`

**Total: 100+ file types supported** - Making RoraFTP truly universal!

## 🔧 Troubleshooting

### Common Issues

**Port already in use:**
```bash
python quick_ftp_server.py --port 9000
```

**Permission denied (Linux/Mac):**
```bash
sudo python quick_ftp_server.py --ftp-port 2121
```

**Firewall blocking connections:**
- Windows: Allow Python through Windows Firewall
- Linux: `sudo ufw allow 8000` and `sudo ufw allow 21`
- Router: Forward ports 8000 and 21 to your computer

**Mobile can't connect:**
- Ensure devices are on the same network
- Check firewall settings
- Try the QR code for automatic connection

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/ForgottenCNZ/RoraFTP/issues)
- 📖 Documentation: [Wiki](https://github.com/ForgottenCNZ/RoraFTP/wiki)

---

**Made with ❤️ for universal file sharing**
