#!/usr/bin/env python3
"""
🚀 RoraFTP - Production-Ready Cloud File Server
Version: 2.0.0
Author: ForgottenCNZ
License: MIT

High-performance cloud file server optimized for all devices with beautiful UI.

✨ Performance Features:
- Dynamic chunk sizing (64KB → 4MB adaptive)
- HTTP keep-alive & smart caching
- Range requests & resume downloads
- Metadata caching & socket optimization
- Up to 10GB file support
- Cloud-themed minimalistic interface
"""

import os
import sys
import socket
import threading
import time
import subprocess
import webbrowser
import logging
import mimetypes
import tempfile
import shutil
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from socketserver import ThreadingMixIn
from urllib.parse import quote, unquote
import argparse
import json
import cgi
import gzip

# Version check
if sys.version_info < (3, 6):
    print("ERROR: Python 3.6+ required")
    sys.exit(1)

# Configuration
MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024  # 10GB - Support for large files
BASE_CHUNK_SIZE = 1024 * 1024  # 1MB base chunk size
MAX_CONNECTIONS = 50
RATE_LIMIT = 50  # requests per minute (generous for real use)

# Performance optimization functions
def get_optimal_chunk_size(file_size):
    """Calculate optimal chunk size based on file size for better performance."""
    if file_size < 10 * 1024 * 1024:  # < 10MB
        return 64 * 1024  # 64KB for small files
    elif file_size < 100 * 1024 * 1024:  # < 100MB
        return 512 * 1024  # 512KB for medium files
    elif file_size < 1024 * 1024 * 1024:  # < 1GB
        return 2 * 1024 * 1024  # 2MB for large files
    else:  # >= 1GB
        return 4 * 1024 * 1024  # 4MB for very large files

def should_compress_content(file_path, content_type):
    """Determine if content should be compressed for better transfer speed."""
    # Don't compress already compressed files
    compressed_extensions = {
        '.zip', '.gz', '.bz2', '.7z', '.rar', '.tar', '.xz',
        '.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.avi',
        '.mp3', '.flac', '.ogg', '.pdf'
    }

    if file_path.suffix.lower() in compressed_extensions:
        return False

    # Compress text-based content
    compressible_types = {
        'text/', 'application/json', 'application/xml',
        'application/javascript', 'application/css',
        'application/html', 'image/svg+xml'
    }

    return any(content_type.startswith(t) for t in compressible_types)

def optimize_socket_settings(sock):
    """Optimize socket settings for better performance."""
    import socket
    try:
        # Enable TCP_NODELAY for low latency
        sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

        # Increase socket buffer sizes for better throughput
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB send buffer
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB receive buffer

        # Enable keep-alive
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
    except Exception:
        # Ignore socket optimization errors
        pass

# Comprehensive file extensions - Universal file type support
ALLOWED_EXTENSIONS = {
    # Documents and Text
    '.txt', '.rtf', '.md', '.markdown', '.rst', '.tex', '.log',
    '.pdf', '.doc', '.docx', '.odt', '.pages',
    '.xls', '.xlsx', '.ods', '.numbers', '.xlsm', '.xlsb',
    '.ppt', '.pptx', '.odp', '.key', '.pps', '.ppsx',

    # Images and Graphics
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif',
    '.svg', '.webp', '.ico', '.psd', '.ai', '.eps', '.raw',
    '.cr2', '.nef', '.arw', '.dng', '.heic', '.heif', '.avif',

    # Audio Files
    '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
    '.opus', '.aiff', '.au', '.ra', '.amr', '.3gp',

    # Video Files
    '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm',
    '.m4v', '.3gp', '.ogv', '.ts', '.mts', '.vob', '.asf',

    # Archives and Compressed
    '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
    '.tar.gz', '.tar.bz2', '.tar.xz', '.dmg', '.iso',

    # Programming and Development
    '.py', '.js', '.html', '.css', '.php', '.java', '.cpp',
    '.c', '.h', '.cs', '.rb', '.go', '.rs', '.swift',
    '.kt', '.scala', '.pl', '.sh', '.bat', '.ps1',
    '.sql', '.r', '.m', '.vb', '.pas', '.asm',

    # Data and Configuration
    '.json', '.xml', '.yaml', '.yml', '.toml', '.ini',
    '.cfg', '.conf', '.properties', '.env', '.csv',
    '.tsv', '.dat', '.db', '.sqlite', '.mdb',

    # Web and Internet
    '.htm', '.xhtml', '.jsp', '.asp', '.aspx', '.erb',
    '.ejs', '.handlebars', '.mustache', '.twig',

    # Fonts
    '.ttf', '.otf', '.woff', '.woff2', '.eot',

    # 3D and CAD
    '.obj', '.fbx', '.dae', '.3ds', '.blend', '.max',
    '.dwg', '.dxf', '.step', '.stp', '.iges', '.igs',

    # Scientific and Academic
    '.mat', '.hdf5', '.nc', '.fits', '.nii', '.dcm',
    '.bib', '.ris', '.endnote',

    # E-books
    '.epub', '.mobi', '.azw', '.azw3', '.fb2', '.lit',

    # Backup and System
    '.bak', '.backup', '.old', '.tmp', '.cache',

    # Miscellaneous
    '.torrent', '.magnet', '.lnk', '.url', '.webloc'
}

# Logging setup with Unicode support
import platform
import time
from functools import lru_cache

# Phase 2 Optimizations: Caching Classes
class FileMetadataCache:
    """Cache file metadata for better performance."""
    def __init__(self, ttl=300):  # 5 minutes TTL
        self.cache = {}
        self.ttl = ttl

    def get_file_info(self, file_path):
        """Get cached file information."""
        cache_key = str(file_path)
        now = time.time()

        if cache_key in self.cache:
            cached_time, info = self.cache[cache_key]
            if now - cached_time < self.ttl:
                return info

        # Generate new info
        try:
            stat = file_path.stat()
            info = {
                'size': stat.st_size,
                'mtime': stat.st_mtime,
                'is_file': file_path.is_file(),
                'is_dir': file_path.is_dir(),
                'exists': True
            }
        except (OSError, FileNotFoundError):
            info = {'exists': False}

        self.cache[cache_key] = (now, info)
        return info

    def invalidate(self, file_path):
        """Invalidate cache entry."""
        cache_key = str(file_path)
        self.cache.pop(cache_key, None)

class DirectoryCache:
    """Cache directory listings for better performance."""
    def __init__(self, ttl=60):  # 1 minute TTL
        self.cache = {}
        self.ttl = ttl

    def get_directory_listing(self, path):
        """Get cached directory listing."""
        cache_key = str(path)
        now = time.time()

        if cache_key in self.cache:
            cached_time, listing = self.cache[cache_key]
            if now - cached_time < self.ttl:
                return listing

        # Generate new listing
        try:
            listing = list(path.iterdir())
        except OSError:
            listing = []

        self.cache[cache_key] = (now, listing)
        return listing

    def invalidate(self, path):
        """Invalidate cache entry."""
        cache_key = str(path)
        self.cache.pop(cache_key, None)

# Global cache instances
file_metadata_cache = FileMetadataCache()
directory_cache = DirectoryCache()

# Create file handler with UTF-8 encoding
file_handler = logging.FileHandler('fileserver.log', encoding='utf-8')
file_handler.setLevel(logging.INFO)

# Create console handler with appropriate encoding
if platform.system() == 'Windows':
    # On Windows, use a safer console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
else:
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

# Create formatter
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Configure logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def print_status(message, status_type="info"):
    """Print colored status messages with safe encoding."""
    # Use ASCII-safe icons for Windows compatibility
    icons = {"success": "[OK]", "error": "[ERROR]", "warning": "[WARN]", "info": "[INFO]"}
    icon = icons.get(status_type, "[INFO]")

    try:
        print(f"{icon} {message}")
    except UnicodeEncodeError:
        # Fallback to ASCII-only
        print(f"[{status_type.upper()}] {message}")

    if status_type == "error":
        logger.error(message)
    elif status_type == "warning":
        logger.warning(message)
    else:
        logger.info(message)


class SimpleRateLimiter:
    """Simple, reliable rate limiter."""
    
    def __init__(self, max_requests=RATE_LIMIT, window=60):
        self.max_requests = max_requests
        self.window = window
        self.requests = {}
        self.lock = threading.Lock()
    
    def is_allowed(self, client_ip):
        """Check if request is allowed."""
        with self.lock:
            now = time.time()
            
            # Clean old entries
            if client_ip in self.requests:
                self.requests[client_ip] = [
                    timestamp for timestamp in self.requests[client_ip]
                    if now - timestamp < self.window
                ]
            else:
                self.requests[client_ip] = []
            
            # Check limit
            if len(self.requests[client_ip]) >= self.max_requests:
                logger.warning(f"Rate limit exceeded for {client_ip}")
                return False
            
            # Add current request
            self.requests[client_ip].append(now)
            return True


class ProductionHTTPServer(ThreadingMixIn, HTTPServer):
    """Optimized thread-safe HTTP server with performance enhancements."""
    allow_reuse_address = True
    daemon_threads = True

    def __init__(self, *args, **kwargs):
        self.rate_limiter = SimpleRateLimiter()
        super().__init__(*args, **kwargs)

        # Optimize server socket
        optimize_socket_settings(self.socket)

    def server_bind(self):
        """Bind server with optimized settings."""
        super().server_bind()
        # Additional socket optimizations after binding
        optimize_socket_settings(self.socket)


class UniversalFileHandler(SimpleHTTPRequestHandler):
    """Universal file handler that works on all devices."""
    
    def __init__(self, *args, **kwargs):
        self.share_path = kwargs.pop('share_path', '.')
        self.config = kwargs.pop('config', {})
        super().__init__(*args, directory=self.share_path, **kwargs)
    
    def log_message(self, format, *args):
        """Enhanced logging with safe encoding."""
        try:
            client_ip = self.client_address[0]
            user_agent = self.headers.get('User-Agent', 'Unknown')
            device = self._detect_device(user_agent)
            message = f"{device} {client_ip} - {format % args}"
            logger.info(message)
        except UnicodeEncodeError:
            # Fallback to ASCII-safe logging
            client_ip = self.client_address[0]
            message = f"Client {client_ip} - {format % args}"
            logger.info(message)
        except Exception as e:
            # Ultimate fallback
            logger.info(f"Request from {self.client_address[0]}")
    
    def _detect_device(self, user_agent):
        """Simple device detection for logging."""
        # Use ASCII-safe device detection for Windows compatibility
        if 'iPhone' in user_agent:
            return "iPhone"
        elif 'Android' in user_agent:
            return "Android"
        elif 'iPad' in user_agent:
            return "iPad"
        elif 'Windows' in user_agent:
            return "Windows"
        elif 'Mac' in user_agent:
            return "Mac"
        else:
            return "Browser"
    
    def do_GET(self):
        """Handle GET requests with rate limiting."""
        client_ip = self.client_address[0]
        
        # Rate limiting
        if not self.server.rate_limiter.is_allowed(client_ip):
            self.send_error(429, "Too many requests")
            return
        
        # Handle special paths
        if self.path == '/health':
            self._send_health_check()
            return
        
        # Enhanced file serving with range support
        self._enhanced_file_serving()

    def _enhanced_file_serving(self):
        """Enhanced file serving with range requests and compression."""
        try:
            # Parse the path
            path = self.translate_path(self.path)
            file_path = Path(path)

            # Check if it's a directory or special path
            if file_path.is_dir() or self.path == '/':
                # Use the standard directory listing
                return super().do_GET()

            # Check if file exists
            if not file_path.exists() or not file_path.is_file():
                # Fall back to standard handling for non-existent files
                return super().do_GET()

            # Get file info from cache
            metadata = file_metadata_cache.get_file_info(file_path)
            if not metadata.get('exists', False):
                self.send_error(404, "File not found")
                return

            file_size = metadata.get('size', 0)
            mtime = metadata.get('mtime', time.time())

            # Handle range requests for partial content
            range_header = self.headers.get('Range')
            if range_header:
                return self._handle_range_request(file_path, file_size, range_header)

            # Determine content type
            content_type, _ = mimetypes.guess_type(str(file_path))
            if content_type is None:
                content_type = 'application/octet-stream'

            # Check if we should compress
            should_compress = should_compress_content(file_path, content_type)

            # Send headers
            self.send_response(200)
            self.send_header('Content-Type', content_type)
            self.send_header('Content-Length', str(file_size))
            self.send_header('Last-Modified', self.date_time_string(mtime))
            self.send_header('Accept-Ranges', 'bytes')

            # Performance headers
            self.send_header('Connection', 'keep-alive')
            self.send_header('Keep-Alive', 'timeout=5, max=100')

            # Cache headers based on file type
            if file_path.suffix.lower() in {'.jpg', '.jpeg', '.png', '.gif', '.css', '.js', '.ico'}:
                self.send_header('Cache-Control', 'public, max-age=3600')
                self.send_header('ETag', f'"{int(mtime)}"')
            else:
                self.send_header('Cache-Control', 'public, max-age=300')

            # For now, disable compression to ensure compatibility
            # TODO: Re-enable compression after more testing
            # if should_compress and file_size < 10 * 1024 * 1024:
            #     accept_encoding = self.headers.get('Accept-Encoding', '')
            #     if 'gzip' in accept_encoding:
            #         self.send_header('Content-Encoding', 'gzip')

            self.end_headers()

            # Send file content without compression for now
            self._send_file_content(file_path, file_size, use_compression=False)

        except Exception as e:
            logger.error(f"Error serving file: {e}")
            self.send_error(500, "Internal server error")

    def _handle_range_request(self, file_path, file_size, range_header):
        """Handle HTTP Range requests for partial content."""
        try:
            # Parse range header (e.g., "bytes=0-1023")
            if not range_header.startswith('bytes='):
                self.send_error(416, "Range not satisfiable")
                return

            ranges = range_header[6:].split(',')
            if len(ranges) != 1:  # Only support single range for now
                self.send_error(416, "Range not satisfiable")
                return

            range_spec = ranges[0].strip()
            if '-' not in range_spec:
                self.send_error(416, "Range not satisfiable")
                return

            start_str, end_str = range_spec.split('-', 1)

            # Parse start and end
            if start_str:
                start = int(start_str)
            else:
                start = 0

            if end_str:
                end = int(end_str)
            else:
                end = file_size - 1

            # Validate range
            if start < 0 or end >= file_size or start > end:
                self.send_error(416, "Range not satisfiable")
                return

            content_length = end - start + 1

            # Send partial content response
            self.send_response(206)  # Partial Content
            self.send_header('Content-Type', mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream')
            self.send_header('Content-Length', str(content_length))
            self.send_header('Content-Range', f'bytes {start}-{end}/{file_size}')
            self.send_header('Accept-Ranges', 'bytes')

            # Performance headers
            self.send_header('Connection', 'keep-alive')
            self.send_header('Keep-Alive', 'timeout=5, max=100')

            self.end_headers()

            # Send partial file content
            self._send_partial_file_content(file_path, start, content_length)

        except ValueError:
            self.send_error(416, "Range not satisfiable")
        except Exception as e:
            logger.error(f"Error handling range request: {e}")
            self.send_error(500, "Internal server error")

    def _send_file_content(self, file_path, file_size, use_compression=False):
        """Send file content with optional compression."""
        try:
            chunk_size = get_optimal_chunk_size(file_size)

            with open(file_path, 'rb') as f:
                if use_compression:
                    # Compress and send
                    import gzip
                    compressed_data = gzip.compress(f.read())
                    self.wfile.write(compressed_data)
                else:
                    # Stream without compression
                    while True:
                        chunk = f.read(chunk_size)
                        if not chunk:
                            break
                        self.wfile.write(chunk)
        except Exception as e:
            logger.error(f"Error sending file content: {e}")

    def _send_partial_file_content(self, file_path, start, length):
        """Send partial file content for range requests."""
        try:
            chunk_size = get_optimal_chunk_size(length)
            bytes_sent = 0

            with open(file_path, 'rb') as f:
                f.seek(start)
                while bytes_sent < length:
                    remaining = length - bytes_sent
                    read_size = min(chunk_size, remaining)
                    chunk = f.read(read_size)
                    if not chunk:
                        break
                    self.wfile.write(chunk)
                    bytes_sent += len(chunk)
        except Exception as e:
            logger.error(f"Error sending partial file content: {e}")

    def do_POST(self):
        """Handle POST requests (uploads)."""
        client_ip = self.client_address[0]
        
        # Rate limiting
        if not self.server.rate_limiter.is_allowed(client_ip):
            self.send_error(429, "Too many requests")
            return
        
        if self.path == '/upload':
            self._handle_upload()
        else:
            self.send_error(404, "Not found")
    
    def _send_health_check(self):
        """Send health check response."""
        response = json.dumps({
            'status': 'healthy',
            'server': 'RoraFTP Cloud File Server',
            'version': '2.0.0',
            'timestamp': time.time()
        })
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response)))
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def _handle_upload(self):
        """Handle file upload using standard cgi.FieldStorage."""
        try:
            # Get content length
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > MAX_FILE_SIZE:
                self._send_upload_error("File too large", 413)
                return
            
            # Parse multipart form data using standard library
            form = cgi.FieldStorage(
                fp=self.rfile,
                headers=self.headers,
                environ={
                    'REQUEST_METHOD': 'POST',
                    'CONTENT_TYPE': self.headers.get('Content-Type', ''),
                    'CONTENT_LENGTH': str(content_length)
                }
            )
            
            uploaded_files = []
            
            # Process each field
            for field_name in form:
                field = form[field_name]
                
                # Skip non-file fields
                if not hasattr(field, 'filename') or not field.filename:
                    continue
                
                # Process file upload
                result = self._process_file_upload(field)
                if result:
                    uploaded_files.append(result)
            
            # Send success response
            if uploaded_files:
                self._send_upload_success(uploaded_files)
                print_status(f"Upload successful: {', '.join(uploaded_files)}", "success")
            else:
                self._send_upload_error("No valid files uploaded")
        
        except Exception as e:
            logger.error(f"Upload error from {self.client_address[0]}: {e}")
            self._send_upload_error("Upload failed")
    
    def _process_file_upload(self, field):
        """Process individual file upload with validation."""
        try:
            filename = field.filename
            if not filename:
                return None
            
            # Validate filename
            safe_filename = self._sanitize_filename(filename)
            if not safe_filename:
                logger.warning(f"Invalid filename rejected: {filename}")
                return None
            
            # Validate file type
            if not self._is_allowed_file_type(safe_filename):
                logger.warning(f"File type not allowed: {safe_filename}")
                return None
            
            # Create full file path
            file_path = Path(self.share_path) / safe_filename
            
            # Check if file already exists and create unique name if needed
            if file_path.exists():
                safe_filename = self._get_unique_filename(safe_filename)
                file_path = Path(self.share_path) / safe_filename
            
            # Stream file to disk
            bytes_written = self._stream_file_to_disk(field, file_path)
            
            if bytes_written > 0:
                logger.info(f"File uploaded: {safe_filename} ({self._format_size(bytes_written)})")

                # Invalidate caches for the uploaded file and directory
                file_metadata_cache.invalidate(file_path)
                directory_cache.invalidate(Path(self.share_path))

                return safe_filename
            
            return None
        
        except Exception as e:
            logger.error(f"Error processing file upload: {e}")
            return None
    
    def _stream_file_to_disk(self, field, file_path):
        """Stream file to disk efficiently with dynamic chunk sizing."""
        total_bytes = 0

        try:
            # Get content length for optimal chunk sizing
            content_length = int(self.headers.get('Content-Length', 0))
            chunk_size = get_optimal_chunk_size(content_length) if content_length > 0 else BASE_CHUNK_SIZE

            with open(file_path, 'wb') as output_file:
                while True:
                    # Read in optimally-sized chunks
                    chunk = field.file.read(chunk_size)
                    if not chunk:
                        break

                    total_bytes += len(chunk)

                    # Check size limit
                    if total_bytes > MAX_FILE_SIZE:
                        output_file.close()
                        file_path.unlink()  # Delete partial file
                        raise ValueError("File too large")

                    output_file.write(chunk)

                    # Adjust chunk size dynamically if we have better size info
                    if total_bytes > chunk_size * 10:  # After 10 chunks, optimize
                        new_chunk_size = get_optimal_chunk_size(total_bytes * 2)  # Estimate final size
                        if new_chunk_size != chunk_size:
                            chunk_size = new_chunk_size

            return total_bytes

        except Exception as e:
            # Clean up on error
            if file_path.exists():
                try:
                    file_path.unlink()
                except:
                    pass
            raise e
    
    def _sanitize_filename(self, filename):
        """Sanitize filename for security."""
        if not filename:
            return None
        
        # Get just the filename, no path
        filename = Path(filename).name
        
        # Remove dangerous characters
        safe_chars = set("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_ ")
        sanitized = ''.join(c for c in filename if c in safe_chars)
        
        # Remove leading/trailing spaces and dots
        sanitized = sanitized.strip('. ')
        
        # Ensure reasonable length
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:255-len(ext)] + ext
        
        # Check final result
        if not sanitized or sanitized.startswith('.') or sanitized in ['..', '.']:
            return None
        
        return sanitized
    
    def _is_allowed_file_type(self, filename):
        """Check if file type is allowed."""
        if not filename:
            return False
        
        extension = Path(filename).suffix.lower()
        return extension in ALLOWED_EXTENSIONS
    
    def _get_unique_filename(self, filename):
        """Generate unique filename if file exists."""
        base_path = Path(self.share_path)
        name = Path(filename).stem
        extension = Path(filename).suffix
        
        counter = 1
        while True:
            new_filename = f"{name}_{counter}{extension}"
            if not (base_path / new_filename).exists():
                return new_filename
            counter += 1
            if counter > 1000:  # Prevent infinite loop
                return f"{name}_{int(time.time())}{extension}"
    
    def _send_upload_success(self, uploaded_files):
        """Send HTML success response (iPhone compatible)."""
        file_list = ', '.join(uploaded_files)
        html = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Upload Complete</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }}

        /* Cloud background animation */
        body::before {{
            content: '';
            position: fixed;
            top: 0; left: 0; width: 100%; height: 100%;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%);
            animation: cloudFloat 20s ease-in-out infinite;
            z-index: -1;
        }}

        @keyframes cloudFloat {{
            0%, 100% {{ transform: translateX(0) translateY(0); }}
            50% {{ transform: translateX(-5px) translateY(10px); }}
        }}

        .container {{
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }}

        .icon {{ font-size: 4em; margin-bottom: 24px; opacity: 0.8; }}
        h1 {{ color: #2c3e50; margin-bottom: 20px; font-weight: 200; font-size: 2.2em; letter-spacing: -1px; }}
        .files {{
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            padding: 16px 20px;
            border-radius: 12px;
            margin: 24px 0;
            border: 1px solid rgba(46, 204, 113, 0.3);
            font-weight: 400;
        }}
        .btn {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-size: 1em;
            margin: 8px;
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
            font-weight: 400;
        }}
        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }}
        .btn-secondary {{
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
        }}
        .btn-secondary:hover {{
            box-shadow: 0 8px 20px rgba(149, 165, 166, 0.4);
        }}

        @media (max-width: 480px) {{
            .container {{ padding: 40px 24px; margin: 12px; }}
            h1 {{ font-size: 1.8em; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">☁</div>
        <h1>Upload Complete</h1>
        <div class="files">Files uploaded to cloud: {file_list}</div>
        <a href="/" class="btn">Back to Cloud</a>
        <a href="javascript:history.back()" class="btn btn-secondary">Go Back</a>
    </div>
    <script>
        // Auto-redirect after 3 seconds
        setTimeout(() => {{ window.location.href = '/'; }}, 3000);
    </script>
</body>
</html>'''
        
        self._send_html_response(html)
    
    def _send_upload_error(self, message, status_code=400):
        """Send HTML error response (iPhone compatible)."""
        html = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Upload Error</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
               text-align: center; padding: 40px; background: linear-gradient(135deg, #dc3545, #c82333); 
               color: white; min-height: 100vh; margin: 0; }}
        .container {{ background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px; 
                     backdrop-filter: blur(10px); max-width: 400px; margin: 0 auto; }}
        .icon {{ font-size: 4rem; margin-bottom: 20px; }}
        h1 {{ margin: 0 0 20px 0; }}
        .message {{ background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 20px 0; }}
        .btn {{ background: white; color: #dc3545; border: none; padding: 12px 24px; 
               border-radius: 8px; text-decoration: none; display: inline-block; 
               font-weight: bold; margin: 10px; }}
        .btn:hover {{ background: #f8f9fa; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">❌</div>
        <h1>Upload Failed</h1>
        <div class="message">{message}</div>
        <a href="/" class="btn">📁 Try Again</a>
        <a href="javascript:history.back()" class="btn">⬅️ Go Back</a>
    </div>
</body>
</html>'''
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def _send_html_response(self, html):
        """Send HTML response with proper headers."""
        html_bytes = html.encode('utf-8')
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_bytes)))
        self.end_headers()
        self.wfile.write(html_bytes)
    
    def list_directory(self, path):
        """Generate enhanced directory listing with caching."""
        path_obj = Path(path)

        # Use cached directory listing for better performance
        file_list = directory_cache.get_directory_listing(path_obj)
        if not file_list:
            self.send_error(404, "Directory not found")
            return None

        # Filter and sort
        file_list = [f for f in file_list if not f.name.startswith('.')]
        file_list.sort(key=lambda x: (x.is_file(), x.name.lower()))
        
        # Generate HTML
        html = self._generate_directory_html(file_list)
        
        # Convert to bytes (disable compression for now to fix issues)
        html_bytes = html.encode('utf-8')
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_bytes)))

        # Performance optimizations
        self.send_header('Connection', 'keep-alive')
        self.send_header('Keep-Alive', 'timeout=5, max=100')
        self.send_header('Cache-Control', 'public, max-age=60')  # Cache for 1 minute

        # Security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')

        self.end_headers()
        
        from io import BytesIO
        f = BytesIO()
        f.write(html_bytes)
        f.seek(0)
        return f
    
    def _generate_directory_html(self, file_list):
        """Generate minimalistic cloud-themed directory HTML."""
        local_ip = self.config.get('local_ip', 'localhost')
        ftp_port = self.config.get('ftp_port', 21)

        html = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cloud Storage</title>
    <style>
        * {{ box-sizing: border-box; margin: 0; padding: 0; }}

        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }}

        /* Cloud background animation */
        body::before {{
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: cloudFloat 20s ease-in-out infinite;
            z-index: -1;
        }}

        @keyframes cloudFloat {{
            0%, 100% {{ transform: translateX(0) translateY(0); }}
            25% {{ transform: translateX(10px) translateY(-5px); }}
            50% {{ transform: translateX(-5px) translateY(10px); }}
            75% {{ transform: translateX(5px) translateY(-10px); }}
        }}

        .container {{
            max-width: 900px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }}

        .header {{
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px 30px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }}

        .header h1 {{
            font-size: 2.8em;
            font-weight: 200;
            color: #2c3e50;
            margin-bottom: 8px;
            letter-spacing: -1px;
        }}

        .header p {{
            color: #7f8c8d;
            font-size: 1.1em;
            font-weight: 300;
        }}

        .info-box {{
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            color: #2c3e50;
            padding: 16px 20px;
            margin: 20px 30px;
            border-radius: 12px;
            font-size: 0.9em;
        }}

        .upload-section {{
            padding: 40px 30px;
            background: rgba(248, 249, 250, 0.5);
        }}

        .upload-area {{
            border: 2px dashed #bdc3c7;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.7);
            position: relative;
        }}

        .upload-area::before {{
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #3498db, #2980b9, #3498db);
            border-radius: 16px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }}

        .upload-area:hover {{
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }}

        .upload-area:hover::before {{
            opacity: 1;
        }}

        .upload-area.dragover {{
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.05);
            transform: scale(1.02);
        }}

        .upload-title {{
            font-size: 1.3em;
            color: #34495e;
            font-weight: 300;
            margin-bottom: 8px;
        }}

        .upload-subtitle {{
            color: #7f8c8d;
            font-size: 0.95em;
            margin-bottom: 20px;
        }}

        .upload-btn {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 1em;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }}

        .upload-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }}

        .files-section {{
            padding: 40px 30px;
        }}

        .files-header {{
            font-size: 1.4em;
            font-weight: 300;
            margin-bottom: 24px;
            color: #2c3e50;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }}

        .file-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
        }}

        .file-card {{
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            backdrop-filter: blur(5px);
        }}

        .file-card:hover {{
            background: rgba(255, 255, 255, 0.95);
            border-color: #3498db;
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }}

        .file-icon {{
            font-size: 0.85em;
            color: #7f8c8d;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            background: #ecf0f1;
            padding: 6px 10px;
            border-radius: 8px;
            margin-bottom: 12px;
            display: inline-block;
        }}

        .file-name {{
            font-weight: 400;
            color: #2c3e50;
            margin-bottom: 8px;
            word-break: break-word;
            font-size: 0.95em;
        }}

        .file-meta {{
            font-size: 0.85em;
            color: #95a5a6;
            font-weight: 300;
        }}

        .empty-state {{
            text-align: center;
            padding: 80px 20px;
            color: #95a5a6;
        }}

        .empty-state .icon {{
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }}

        .empty-state h3 {{
            font-weight: 300;
            margin-bottom: 8px;
            color: #7f8c8d;
        }}

        .empty-state p {{
            font-weight: 300;
            font-size: 0.9em;
        }}

        .footer {{
            background: rgba(248, 249, 250, 0.5);
            padding: 24px;
            text-align: center;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 0.85em;
            color: #95a5a6;
            font-weight: 300;
        }}

        @media (max-width: 768px) {{
            .container {{ margin: 12px; }}
            .header {{ padding: 30px 20px; }}
            .header h1 {{ font-size: 2.2em; }}
            .files-section, .upload-section {{ padding: 30px 20px; }}
            .file-grid {{ grid-template-columns: 1fr; }}
            .upload-area {{ padding: 30px 20px; }}
        }}

        .hidden {{ display: none; }}
        .progress {{
            width: 100%;
            height: 4px;
            background: #ecf0f1;
            border-radius: 2px;
            margin: 16px 0;
            overflow: hidden;
        }}
        .progress-bar {{
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Cloud Storage</h1>
            <p>Simple and secure file sharing in the cloud</p>
        </div>

        <div class="info-box">
            <strong>Mobile Access:</strong> Works in any browser.
            For iOS Files app: <code>ftp://{local_ip}:{ftp_port}</code>
        </div>

        <div class="upload-section">
            <form id="uploadForm" action="/upload" method="post" enctype="multipart/form-data">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-title">Upload to Cloud</div>
                    <div class="upload-subtitle">Drag and drop files here or click to browse</div>
                    <div class="upload-subtitle">Max file size: {self._format_size(MAX_FILE_SIZE)} • 100+ file types supported</div>
                    <input type="file" id="fileInput" name="file" multiple style="display: none;">
                    <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click();">
                        Browse Files
                    </button>
                </div>
                <div class="progress hidden" id="uploadProgress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
            </form>
        </div>

        <div class="files-section">
            <div class="files-header">Cloud Files ({len([f for f in file_list if f.is_file()])} files, {len([f for f in file_list if f.is_dir()])} folders)</div>'''

        if not file_list:
            html += '''
            <div class="empty-state">
                <div class="icon">☁</div>
                <h3>Your cloud storage is empty</h3>
                <p>Upload files to get started</p>
            </div>'''
        else:
            html += '<div class="file-grid">'
            for file_path in file_list:
                # Use cached metadata for better performance
                metadata = file_metadata_cache.get_file_info(file_path)

                if not metadata.get('exists', False):
                    continue

                if metadata.get('is_dir', False):
                    icon = "[DIR]"
                    href = quote(file_path.name) + "/"
                    size_info = "Folder"
                else:
                    icon = self._get_file_icon(file_path.suffix)
                    href = quote(file_path.name)
                    size = metadata.get('size', 0)
                    size_info = self._format_size(size) if size > 0 else "Unknown"

                html += f'''
                <div class="file-card" onclick="window.location.href='{href}'">
                    <div class="file-icon">{icon}</div>
                    <div class="file-name">{file_path.name}</div>
                    <div class="file-meta">{size_info}</div>
                </div>'''
            html += '</div>'

        html += f'''
        </div>

        <div class="footer">
            🚀 Powered by RoraFTP v2.0 - High-Performance Cloud File Server
        </div>
    </div>

    <script>
        // Enhanced drag and drop with proper iPhone support
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadForm = document.getElementById('uploadForm');
        const uploadProgress = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {{
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }});

        uploadArea.addEventListener('dragleave', () => {{
            uploadArea.classList.remove('dragover');
        }});

        uploadArea.addEventListener('drop', (e) => {{
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        }});

        // File input change
        fileInput.addEventListener('change', (e) => {{
            handleFiles(e.target.files);
        }});

        function handleFiles(files) {{
            if (files.length === 0) return;

            // Validate files
            const maxSize = {MAX_FILE_SIZE};
            const allowedExts = {list(ALLOWED_EXTENSIONS)};

            let validFiles = [];
            for (let file of files) {{
                // Size check
                if (file.size > maxSize) {{
                    alert(`File "${{file.name}}" is too large (max {self._format_size(MAX_FILE_SIZE)})`);
                    continue;
                }}

                // Type check
                const ext = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedExts.includes(ext)) {{
                    alert(`File type "${{ext}}" is not allowed`);
                    continue;
                }}

                validFiles.push(file);
            }}

            if (validFiles.length === 0) return;

            // Upload files
            uploadFiles(validFiles);
        }}

        function uploadFiles(files) {{
            const formData = new FormData();

            for (let file of files) {{
                formData.append('file', file);
            }}

            // Show progress
            uploadProgress.classList.remove('hidden');
            progressBar.style.width = '0%';

            // Create XHR for progress tracking
            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', (e) => {{
                if (e.lengthComputable) {{
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }}
            }});

            xhr.addEventListener('load', () => {{
                if (xhr.status === 200) {{
                    // Success - reload page to show uploaded files
                    window.location.reload();
                }} else {{
                    alert('Upload failed. Please try again.');
                    uploadProgress.classList.add('hidden');
                }}
            }});
            
            xhr.addEventListener('error', () => {{
                alert('Network error during upload.');
                uploadProgress.classList.add('hidden');
            }});
            
            xhr.open('POST', '/upload');
            xhr.send(formData);
        }}
    </script>
</body>
</html>'''
        
        return html
    
    def _get_file_icon(self, extension):
        """Get appropriate icon for file type - comprehensive mapping."""
        ext = extension.lower()

        # Use text-based icons for better compatibility
        icons = {
            # Documents
            '.pdf': '[PDF]', '.doc': '[DOC]', '.docx': '[DOC]', '.odt': '[DOC]', '.pages': '[DOC]',
            '.txt': '[TXT]', '.rtf': '[RTF]', '.md': '[MD]', '.markdown': '[MD]', '.rst': '[RST]',
            '.tex': '[TEX]', '.log': '[LOG]',

            # Spreadsheets
            '.xls': '[XLS]', '.xlsx': '[XLS]', '.ods': '[XLS]', '.numbers': '[XLS]',
            '.xlsm': '[XLS]', '.xlsb': '[XLS]', '.csv': '[CSV]', '.tsv': '[TSV]',

            # Presentations
            '.ppt': '[PPT]', '.pptx': '[PPT]', '.odp': '[PPT]', '.key': '[PPT]',
            '.pps': '[PPT]', '.ppsx': '[PPT]',

            # Images
            '.jpg': '[IMG]', '.jpeg': '[IMG]', '.png': '[IMG]', '.gif': '[IMG]',
            '.bmp': '[IMG]', '.tiff': '[IMG]', '.tif': '[IMG]', '.svg': '[SVG]',
            '.webp': '[IMG]', '.ico': '[ICO]', '.psd': '[PSD]', '.ai': '[AI]',
            '.eps': '[EPS]', '.raw': '[RAW]', '.cr2': '[RAW]', '.nef': '[RAW]',
            '.arw': '[RAW]', '.dng': '[RAW]', '.heic': '[IMG]', '.heif': '[IMG]', '.avif': '[IMG]',

            # Audio
            '.mp3': '[MP3]', '.wav': '[WAV]', '.flac': '[FLAC]', '.aac': '[AAC]',
            '.ogg': '[OGG]', '.wma': '[WMA]', '.m4a': '[M4A]', '.opus': '[OPUS]',
            '.aiff': '[AIFF]', '.au': '[AU]', '.ra': '[RA]', '.amr': '[AMR]',

            # Video
            '.mp4': '[MP4]', '.avi': '[AVI]', '.mov': '[MOV]', '.mkv': '[MKV]',
            '.wmv': '[WMV]', '.flv': '[FLV]', '.webm': '[WEBM]', '.m4v': '[M4V]',
            '.3gp': '[3GP]', '.ogv': '[OGV]', '.ts': '[TS]', '.mts': '[MTS]',
            '.vob': '[VOB]', '.asf': '[ASF]',

            # Archives
            '.zip': '[ZIP]', '.rar': '[RAR]', '.7z': '[7Z]', '.tar': '[TAR]',
            '.gz': '[GZ]', '.bz2': '[BZ2]', '.xz': '[XZ]', '.dmg': '[DMG]', '.iso': '[ISO]',

            # Programming
            '.py': '[PY]', '.js': '[JS]', '.html': '[HTML]', '.css': '[CSS]',
            '.php': '[PHP]', '.java': '[JAVA]', '.cpp': '[CPP]', '.c': '[C]',
            '.h': '[H]', '.cs': '[CS]', '.rb': '[RB]', '.go': '[GO]',
            '.rs': '[RS]', '.swift': '[SWIFT]', '.kt': '[KT]', '.scala': '[SCALA]',
            '.pl': '[PL]', '.sh': '[SH]', '.bat': '[BAT]', '.ps1': '[PS1]',
            '.sql': '[SQL]', '.r': '[R]', '.m': '[M]', '.vb': '[VB]',
            '.pas': '[PAS]', '.asm': '[ASM]',

            # Data
            '.json': '[JSON]', '.xml': '[XML]', '.yaml': '[YAML]', '.yml': '[YAML]',
            '.toml': '[TOML]', '.ini': '[INI]', '.cfg': '[CFG]', '.conf': '[CONF]',
            '.properties': '[PROP]', '.env': '[ENV]', '.dat': '[DAT]',
            '.db': '[DB]', '.sqlite': '[DB]', '.mdb': '[DB]',

            # Web
            '.htm': '[HTML]', '.xhtml': '[HTML]', '.jsp': '[JSP]', '.asp': '[ASP]',
            '.aspx': '[ASPX]', '.erb': '[ERB]', '.ejs': '[EJS]',
            '.handlebars': '[HBS]', '.mustache': '[MUST]', '.twig': '[TWIG]',

            # Fonts
            '.ttf': '[FONT]', '.otf': '[FONT]', '.woff': '[FONT]', '.woff2': '[FONT]', '.eot': '[FONT]',

            # 3D/CAD
            '.obj': '[3D]', '.fbx': '[3D]', '.dae': '[3D]', '.3ds': '[3D]',
            '.blend': '[3D]', '.max': '[3D]', '.dwg': '[CAD]', '.dxf': '[CAD]',
            '.step': '[CAD]', '.stp': '[CAD]', '.iges': '[CAD]', '.igs': '[CAD]',

            # Scientific
            '.mat': '[MAT]', '.hdf5': '[HDF5]', '.nc': '[NC]', '.fits': '[FITS]',
            '.nii': '[NII]', '.dcm': '[DCM]', '.bib': '[BIB]', '.ris': '[RIS]',
            '.endnote': '[END]',

            # E-books
            '.epub': '[EPUB]', '.mobi': '[MOBI]', '.azw': '[AZW]', '.azw3': '[AZW]',
            '.fb2': '[FB2]', '.lit': '[LIT]',

            # System/Backup
            '.bak': '[BAK]', '.backup': '[BAK]', '.old': '[OLD]', '.tmp': '[TMP]',
            '.cache': '[CACHE]', '.torrent': '[TORR]', '.magnet': '[MAG]',
            '.lnk': '[LNK]', '.url': '[URL]', '.webloc': '[URL]'
        }

        return icons.get(ext, '[FILE]')
    
    def _format_size(self, size):
        """Format file size in human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"


class QRCodeManager:
    """Handle QR code generation with fallbacks."""
    
    @staticmethod
    def ensure_qrcode_installed():
        """Install qrcode if not available."""
        try:
            import qrcode
            return True
        except ImportError:
            try:
                print_status("Installing QR code support...", "info")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "qrcode[pil]"
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print_status("QR code support installed", "success")
                return True
            except Exception as e:
                print_status(f"Could not install QR code support: {e}", "warning")
                return False
    
    @staticmethod
    def generate_qr(url):
        """Generate QR code with fallback."""
        if not QRCodeManager.ensure_qrcode_installed():
            return None
        
        try:
            import qrcode
            
            # Generate ASCII QR for terminal
            qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, 
                              box_size=1, border=1)
            qr.add_data(url)
            qr.make(fit=True)
            
            modules = qr.get_matrix()
            ascii_lines = []
            for row in modules:
                line = ""
                for module in row:
                    line += "██" if module else "  "
                ascii_lines.append(line)
            
            ascii_qr = "\n".join(ascii_lines)
            
            # Generate image file
            image_qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, 
                                   box_size=10, border=4)
            image_qr.add_data(url)
            image_qr.make(fit=True)
            
            img = image_qr.make_image(fill_color="black", back_color="white")
            img.save("fileserver_qr.png")
            
            return {
                'ascii': ascii_qr,
                'url': url,
                'image_file': 'fileserver_qr.png'
            }
        
        except Exception as e:
            print_status(f"QR generation failed: {e}", "warning")
            return None


def get_local_ip():
    """Get local IP address reliably."""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'


def create_config():
    """Create configuration with sensible defaults."""
    config = {
        'share_path': str(Path.home() / 'FileShare'),
        'http_port': 8000,
        'ftp_port': 21,
        'local_ip': get_local_ip(),
        'max_file_size': MAX_FILE_SIZE,
        'enable_qr': True,
        'auto_open_browser': True
    }
    
    # Save config
    try:
        config_file = Path("fileserver_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        print_status(f"Config saved to {config_file}", "success")
    except Exception as e:
        print_status(f"Could not save config: {e}", "warning")
    
    return config


def create_share_folder(path):
    """Create share folder with sample content."""
    try:
        share_path = Path(path)
        share_path.mkdir(parents=True, exist_ok=True)
        
        # Create welcome file
        readme_path = share_path / "Welcome.txt"
        if not readme_path.exists():
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write("Welcome to Universal File Server!\n")
                f.write("=" * 35 + "\n\n")
                f.write("This server works on:\n")
                f.write("✅ iPhone Safari\n")
                f.write("✅ Android Chrome\n")
                f.write("✅ Desktop browsers\n")
                f.write("✅ iOS Files app\n")
                f.write("✅ FTP clients\n\n")
                f.write("Upload files by dragging them to the web interface\n")
                f.write("or use any FTP client for advanced features.\n\n")
                f.write("Enjoy seamless file sharing!\n")
        
        print_status(f"Share folder ready: {path}", "success")
        return True
    
    except Exception as e:
        print_status(f"Failed to create share folder: {e}", "error")
        return False


def start_ftp_server(config):
    """Start FTP server if available."""
    try:
        try:
            import pyftpdlib
        except ImportError:
            print_status("Installing FTP support...", "info")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyftpdlib"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        from pyftpdlib.authorizers import DummyAuthorizer
        from pyftpdlib.handlers import FTPHandler
        from pyftpdlib.servers import FTPServer
        
        # Simple, reliable FTP setup
        authorizer = DummyAuthorizer()
        authorizer.add_anonymous(config['share_path'], perm="elradfmw")
        
        handler = FTPHandler
        handler.authorizer = authorizer
        handler.banner = "220 Universal FTP Server Ready"
        handler.masquerade_address = config['local_ip']
        handler.passive_ports = range(21100, 21200)
        
        server = FTPServer(('0.0.0.0', config['ftp_port']), handler)
        server.max_cons = MAX_CONNECTIONS
        server.max_cons_per_ip = 10
        
        def run_ftp():
            print_status(f"FTP server started on port {config['ftp_port']}", "success")
            server.serve_forever()
        
        ftp_thread = threading.Thread(target=run_ftp, daemon=True)
        ftp_thread.start()
        
        return server
    
    except Exception as e:
        print_status(f"FTP server failed: {e}", "warning")
        return None


def start_http_server(config):
    """Start HTTP server."""
    try:
        def handler_factory(*args, **kwargs):
            return UniversalFileHandler(*args, share_path=config['share_path'], config=config, **kwargs)
        
        server = ProductionHTTPServer(('0.0.0.0', config['http_port']), handler_factory)
        
        def run_http():
            print_status(f"HTTP server started on port {config['http_port']}", "success")
            server.serve_forever()
        
        http_thread = threading.Thread(target=run_http, daemon=True)
        http_thread.start()
        
        return server
    
    except Exception as e:
        print_status(f"HTTP server failed: {e}", "error")
        return None


def show_server_info(config, http_server, ftp_server):
    """Display server information."""
    local_ip = config['local_ip']
    
    print("\n" + "=" * 70)
    print_status("🚀 RORAFTP CLOUD FILE SERVER", "success")
    print("=" * 70)

    print(f"\nShare Folder: {config['share_path']}")

    if http_server:
        http_url = f"http://{local_ip}:{config['http_port']}"
        print(f"\nHTTP Server: {http_url}")
        print(f"   iPhone Safari: Fully compatible")
        print(f"   Android Chrome: Fully compatible")
        print(f"   Desktop browsers: All supported")

    if ftp_server:
        ftp_url = f"ftp://{local_ip}:{config['ftp_port']}"
        print(f"\nFTP Server: {ftp_url}")
        print(f"   iOS Files app: Connect to Server")
        print(f"   Desktop FTP clients: FileZilla, etc.")
    
    # QR Code
    if config.get('enable_qr') and http_server:
        print(f"\nQR Code for Mobile Access:")
        qr_result = QRCodeManager.generate_qr(http_url)
        if qr_result:
            print(qr_result['ascii'])
            print(f"\nScan with phone camera for instant access!")
            print(f"QR image saved: {qr_result['image_file']}")
        else:
            print(f"Manual URL: {http_url}")

    print(f"\nQuick Mobile Setup:")
    if http_server:
        print(f"   1. Open phone browser -> {http_url}")
    if ftp_server:
        print(f"   2. iOS Files app -> {ftp_url}")

    print(f"\nProduction Features:")
    print(f"   Multi-threaded: {MAX_CONNECTIONS} concurrent users")
    print(f"   Secure: File validation, rate limiting")
    print(f"   Universal: iPhone, Android, desktop compatible")
    print(f"   Fast: Streaming uploads, compressed responses")
    print(f"   Robust: Standard libraries, proven code")

    print(f"\nPress CTRL+C to stop")
    print("=" * 70)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='🚀 RoraFTP - Production-Ready Cloud File Server')
    parser.add_argument('--port', type=int, help='HTTP port (default: 8000)')
    parser.add_argument('--ftp-port', type=int, help='FTP port (default: 21)')
    parser.add_argument('--share', help='Share folder path')
    parser.add_argument('--no-ftp', action='store_true', help='Disable FTP server')
    parser.add_argument('--no-qr', action='store_true', help='Disable QR codes')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t auto-open browser')
    
    args = parser.parse_args()
    
    print("\n🚀 RoraFTP - Cloud File Server")
    print("=" * 45)
    
    # Create configuration
    config = create_config()
    
    # Apply overrides
    if args.port:
        config['http_port'] = args.port
    if args.ftp_port:
        config['ftp_port'] = args.ftp_port
    if args.share:
        config['share_path'] = args.share
    if args.no_qr:
        config['enable_qr'] = False
    if args.no_browser:
        config['auto_open_browser'] = False
    
    # Create share folder
    if not create_share_folder(config['share_path']):
        return
    
    # Start servers
    print_status("Starting servers...", "info")
    
    http_server = start_http_server(config)
    ftp_server = None if args.no_ftp else start_ftp_server(config)
    
    if not http_server:
        print_status("Failed to start HTTP server", "error")
        return
    
    # Show server info
    show_server_info(config, http_server, ftp_server)
    
    # Auto-open browser
    if http_server and config.get('auto_open_browser'):
        time.sleep(1)
        try:
            webbrowser.open(f"http://{config['local_ip']}:{config['http_port']}")
        except:
            pass
    
    # Keep running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print_status("Server stopped by user", "warning")


if __name__ == "__main__":
    main()