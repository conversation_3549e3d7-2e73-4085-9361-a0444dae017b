# RoraFTP API Documentation

RoraFTP provides both a web interface and RESTful API endpoints for programmatic access.

## 🌐 Base URL

```
http://YOUR_SERVER_IP:8000
```

## 📋 API Endpoints

### 1. Health Check

**GET** `/health`

Check if the server is running and healthy.

**Response:**
```json
{
  "status": "healthy",
  "server": "Universal File Server",
  "timestamp": **********.123
}
```

**Status Codes:**
- `200` - Server is healthy
- `500` - Server error

**Example:**
```bash
curl http://localhost:8000/health
```

### 2. File Listing (Web Interface)

**GET** `/`

Returns the main web interface with file listing.

**Response:** HTML page with:
- File upload interface
- Directory listing
- Mobile-friendly design
- QR code for mobile access

**Example:**
```bash
curl http://localhost:8000/
```

### 3. File Upload

**POST** `/upload`

Upload one or more files to the server.

**Content-Type:** `multipart/form-data`

**Parameters:**
- `file` - File(s) to upload (supports multiple files)

**Response:** HTML success/error page

**File Restrictions:**
- Max size: 100MB (configurable)
- Allowed extensions: See [Supported File Types](#supported-file-types)
- Filename sanitization applied

**Example:**
```bash
# Single file
curl -X POST -F "file=@document.pdf" http://localhost:8000/upload

# Multiple files
curl -X POST -F "file=@file1.jpg" -F "file=@file2.png" http://localhost:8000/upload
```

**JavaScript Example:**
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('/upload', {
    method: 'POST',
    body: formData
})
.then(response => response.text())
.then(html => {
    // Handle response HTML
});
```

### 4. File Download

**GET** `/filename`

Download a specific file from the server.

**Parameters:**
- `filename` - Name of the file to download (URL encoded)

**Response:** File content with appropriate headers

**Example:**
```bash
curl -O http://localhost:8000/document.pdf
```

### 5. Directory Navigation

**GET** `/directory/`

Navigate to subdirectories (if enabled).

**Response:** HTML directory listing

**Example:**
```bash
curl http://localhost:8000/subfolder/
```

## 🔒 Security Features

### Rate Limiting
- Default: 50 requests per minute per IP
- Configurable via environment variables
- Returns `429 Too Many Requests` when exceeded

### File Validation
- File type whitelist enforcement
- Filename sanitization
- Size limit enforcement
- No executable file uploads

### Request Headers
All responses include security headers:
```
Cache-Control: no-cache
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
```

## 📱 Mobile API Usage

### QR Code Access
The server generates QR codes for easy mobile access:
- ASCII QR code in terminal output
- PNG image saved as `fileserver_qr.png`
- Contains the server URL for instant connection

### Mobile-Optimized Responses
- Touch-friendly interface
- Responsive design
- Drag-and-drop support
- Progress indicators

## 🔧 Configuration API

### Environment Variables
Configure the server behavior:

```bash
export RORAFTP_PORT=8000
export RORAFTP_FTP_PORT=21
export RORAFTP_SHARE_PATH=/path/to/files
export RORAFTP_MAX_FILE_SIZE=104857600  # 100MB
export RORAFTP_RATE_LIMIT=50
export RORAFTP_MAX_CONNECTIONS=50
export RORAFTP_CHUNK_SIZE=65536  # 64KB
```

### Configuration File
Auto-generated `fileserver_config.json`:
```json
{
  "share_path": "/home/<USER>/FileShare",
  "http_port": 8000,
  "ftp_port": 21,
  "local_ip": "*************",
  "max_file_size": 104857600,
  "enable_qr": true,
  "auto_open_browser": true
}
```

## 📊 Response Formats

### Success Responses

**File Upload Success:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Upload Successful</title>
    <!-- Mobile-optimized CSS -->
</head>
<body>
    <div class="container">
        <div class="icon">✅</div>
        <h1>Upload Successful!</h1>
        <div class="files">Files uploaded: filename.pdf</div>
        <!-- Navigation buttons -->
    </div>
</body>
</html>
```

**Health Check:**
```json
{
  "status": "healthy",
  "server": "Universal File Server",
  "timestamp": **********.123
}
```

### Error Responses

**Upload Error:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Upload Error</title>
</head>
<body>
    <div class="container">
        <div class="icon">❌</div>
        <h1>Upload Failed</h1>
        <div class="message">Error message here</div>
    </div>
</body>
</html>
```

**Rate Limit Exceeded:**
```
HTTP/1.1 429 Too Many Requests
Content-Type: text/html

<html>
<body>
<h1>Error 429: Too Many Requests</h1>
<p>Rate limit exceeded. Please try again later.</p>
</body>
</html>
```

## 🔌 FTP API

### Connection Details
- **Host:** Server IP address
- **Port:** 21 (default) or custom
- **Username:** anonymous
- **Password:** (empty)
- **Mode:** Passive (recommended)

### Supported FTP Commands
- `LIST` - Directory listing
- `RETR` - Download files
- `STOR` - Upload files
- `DELE` - Delete files
- `MKD` - Create directories
- `RMD` - Remove directories
- `PWD` - Current directory
- `CWD` - Change directory

### FTP Client Examples

**Command Line (Linux/Mac):**
```bash
ftp YOUR_SERVER_IP
# Username: anonymous
# Password: (press enter)
```

**FileZilla:**
- Host: `YOUR_SERVER_IP`
- Port: `21`
- Protocol: `FTP`
- Logon Type: `Anonymous`

**iOS Files App:**
1. Open Files app
2. Tap "..." → "Connect to Server"
3. Enter: `ftp://YOUR_SERVER_IP:21`

## 📝 Supported File Types (Universal Support)

RoraFTP supports **100+ file types** across all major categories:

### Documents & Text (20+ types)
- **Text**: `.txt`, `.rtf`, `.md`, `.markdown`, `.rst`, `.tex`, `.log`
- **Office**: `.pdf`, `.doc`, `.docx`, `.odt`, `.pages`, `.xls`, `.xlsx`, `.ppt`, `.pptx`
- **E-books**: `.epub`, `.mobi`, `.azw`, `.azw3`, `.fb2`, `.lit`

### Images & Graphics (25+ types)
- **Standard**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.tiff`, `.webp`, `.svg`
- **Professional**: `.psd`, `.ai`, `.eps`, `.raw`, `.cr2`, `.nef`, `.arw`, `.dng`
- **Modern**: `.heic`, `.heif`, `.avif`, `.ico`

### Audio & Video (20+ types)
- **Audio**: `.mp3`, `.wav`, `.flac`, `.aac`, `.ogg`, `.wma`, `.m4a`, `.opus`, `.aiff`
- **Video**: `.mp4`, `.avi`, `.mov`, `.mkv`, `.wmv`, `.flv`, `.webm`, `.m4v`, `.3gp`

### Programming & Development (25+ types)
- **Languages**: `.py`, `.js`, `.html`, `.css`, `.php`, `.java`, `.cpp`, `.c`, `.cs`, `.go`, `.rs`
- **Scripts**: `.sh`, `.bat`, `.ps1`, `.pl`, `.sql`, `.r`, `.m`

### Data & Configuration (15+ types)
- **Data**: `.json`, `.xml`, `.yaml`, `.yml`, `.csv`, `.tsv`, `.toml`
- **Config**: `.ini`, `.cfg`, `.conf`, `.properties`, `.env`
- **Database**: `.db`, `.sqlite`, `.mdb`

### Archives & Specialized (15+ types)
- **Archives**: `.zip`, `.rar`, `.7z`, `.tar`, `.gz`, `.bz2`, `.xz`, `.dmg`, `.iso`
- **Fonts**: `.ttf`, `.otf`, `.woff`, `.woff2`, `.eot`
- **3D/CAD**: `.obj`, `.fbx`, `.blend`, `.dwg`, `.dxf`

**Security Note**: All file types go through the same validation and sanitization process.

## 🚨 Error Codes

| Code | Description | Cause |
|------|-------------|-------|
| 200 | OK | Request successful |
| 400 | Bad Request | Invalid upload or malformed request |
| 404 | Not Found | File or endpoint not found |
| 413 | Payload Too Large | File exceeds size limit |
| 415 | Unsupported Media Type | File type not allowed |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |

## 🔍 Logging

### Log Levels
- `INFO` - Normal operations
- `WARNING` - Rate limits, file type rejections
- `ERROR` - Upload failures, server errors

### Log Format
```
2024-01-01 12:00:00,123 - INFO - 📱iPhone ************* - "POST /upload HTTP/1.1" 200 -
```

### Log Files
- `fileserver.log` - Application logs
- `server_security.log` - Security events

## 🧪 Testing

### Health Check Test
```bash
curl -f http://localhost:8000/health || echo "Server not healthy"
```

### Upload Test
```bash
echo "test content" > test.txt
curl -X POST -F "file=@test.txt" http://localhost:8000/upload
```

### Load Test
```bash
# Simple load test
for i in {1..10}; do
  curl http://localhost:8000/health &
done
wait
```

---

For more advanced usage and integration examples, see the [GitHub Wiki](https://github.com/ForgottenCNZ/RoraFTP/wiki).
