# RoraFTP Systemd Service File
# Install to: /etc/systemd/system/roraftp.service
# 
# Installation:
# 1. sudo cp roraftp.service /etc/systemd/system/
# 2. sudo systemctl daemon-reload
# 3. sudo systemctl enable roraftp
# 4. sudo systemctl start roraftp
#
# Management:
# - sudo systemctl status roraftp
# - sudo systemctl stop roraftp
# - sudo systemctl restart roraftp
# - sudo journalctl -u roraftp -f

[Unit]
Description=RoraFTP Universal File Server
Documentation=https://github.com/ForgottenCNZ/RoraFTP
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=roraftp
Group=roraftp
WorkingDirectory=/opt/roraftp
ExecStart=/usr/bin/python3 /opt/roraftp/quick_ftp_server.py --share /var/lib/roraftp/files --no-browser
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Environment variables
Environment=RORAFTP_PORT=8000
Environment=RORAFTP_FTP_PORT=21
Environment=RORAFTP_SHARE_PATH=/var/lib/roraftp/files
Environment=RORAFTP_MAX_FILE_SIZE=104857600
Environment=RORAFTP_RATE_LIMIT=50
Environment=RORAFTP_AUTO_OPEN_BROWSER=false
Environment=PYTHONUNBUFFERED=1

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/roraftp
ReadOnlyPaths=/opt/roraftp

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=roraftp

[Install]
WantedBy=multi-user.target
