#!/usr/bin/env python3
"""
RoraFTP Deployment Script
Automated deployment and setup for RoraFTP Universal File Server
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def print_status(message, status="info"):
    """Print status with simple icons."""
    icons = {"success": "[OK]", "error": "[ERROR]", "warning": "[WARN]", "info": "[INFO]"}
    print(f"{icons.get(status, '[INFO]')} {message}")

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 6):
        print_status("Python 3.6+ required", "error")
        return False
    print_status(f"Python {sys.version_info.major}.{sys.version_info.minor} detected", "success")
    return True

def install_dependencies():
    """Install required dependencies."""
    print_status("Installing dependencies...", "info")
    
    dependencies = ["qrcode[pil]", "pyftpdlib"]
    
    for dep in dependencies:
        try:
            print_status(f"Installing {dep}...", "info")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print_status(f"{dep} installed successfully", "success")
        except subprocess.CalledProcessError:
            print_status(f"Failed to install {dep} (will auto-install when needed)", "warning")

def create_directories():
    """Create necessary directories."""
    directories = [
        "files",
        "logs", 
        "config"
    ]
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print_status(f"Created directory: {directory}", "success")
        else:
            print_status(f"Directory exists: {directory}", "info")

def create_sample_files():
    """Create sample files for testing."""
    files_dir = Path("files")
    
    # Create welcome file
    welcome_file = files_dir / "Welcome.txt"
    if not welcome_file.exists():
        with open(welcome_file, 'w', encoding='utf-8') as f:
            f.write("Welcome to RoraFTP Universal File Server!\n")
            f.write("=" * 40 + "\n\n")
            f.write("This server is now ready for use.\n\n")
            f.write("Features:\n")
            f.write("- Web interface for file upload/download\n")
            f.write("- FTP server for advanced clients\n")
            f.write("- Mobile-friendly design\n")
            f.write("- Cross-platform compatibility\n\n")
            f.write("Upload files through the web interface or use FTP clients.\n")
            f.write("Enjoy seamless file sharing!\n")
        print_status("Created welcome file", "success")

def setup_systemd_service():
    """Setup systemd service on Linux."""
    if platform.system() != "Linux":
        return
    
    service_content = """[Unit]
Description=RoraFTP Universal File Server
After=network.target

[Service]
Type=simple
User=roraftp
WorkingDirectory=/opt/roraftp
ExecStart=/usr/bin/python3 /opt/roraftp/quick_ftp_server.py --share /var/lib/roraftp/files --no-browser
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open("roraftp.service", "w") as f:
            f.write(service_content)
        print_status("Systemd service file created", "success")
        print_status("To install: sudo cp roraftp.service /etc/systemd/system/", "info")
    except Exception as e:
        print_status(f"Failed to create service file: {e}", "warning")

def run_tests():
    """Run the test suite."""
    print_status("Running tests...", "info")
    
    if not Path("test_server.py").exists():
        print_status("Test file not found, skipping tests", "warning")
        return True
    
    try:
        result = subprocess.run([sys.executable, "test_server.py"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print_status("All tests passed!", "success")
            return True
        else:
            print_status("Some tests failed", "warning")
            print(result.stdout)
            return False
    except subprocess.TimeoutExpired:
        print_status("Tests timed out", "warning")
        return False
    except Exception as e:
        print_status(f"Test execution failed: {e}", "warning")
        return False

def get_network_info():
    """Get network information."""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "localhost"

def main():
    """Main deployment function."""
    print("=" * 60)
    print("RoraFTP Universal File Server - Deployment Script")
    print("=" * 60)
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    # Check if main server file exists
    if not Path("quick_ftp_server.py").exists():
        print_status("quick_ftp_server.py not found!", "error")
        print_status("Please run this script from the RoraFTP directory", "info")
        sys.exit(1)
    
    # Setup steps
    print_status("Starting deployment setup...", "info")
    
    # Install dependencies
    install_dependencies()
    
    # Create directories
    create_directories()
    
    # Create sample files
    create_sample_files()
    
    # Setup service file
    setup_systemd_service()
    
    # Run tests
    tests_passed = run_tests()
    
    # Get network info
    local_ip = get_network_info()
    
    # Final status
    print("\n" + "=" * 60)
    print_status("DEPLOYMENT COMPLETE", "success")
    print("=" * 60)
    
    print(f"\nServer Information:")
    print(f"  Local IP: {local_ip}")
    print(f"  HTTP URL: http://{local_ip}:8000")
    print(f"  FTP URL: ftp://{local_ip}:21")
    print(f"  Files Directory: {Path('files').absolute()}")
    
    print(f"\nQuick Start:")
    print(f"  python quick_ftp_server.py")
    
    print(f"\nCustom Configuration:")
    print(f"  python quick_ftp_server.py --port 9000 --share ./custom_files")
    
    print(f"\nDocker Deployment:")
    print(f"  docker build -t roraftp .")
    print(f"  docker run -p 8000:8000 -p 21:21 -v ./files:/app/files roraftp")
    
    if platform.system() == "Linux":
        print(f"\nSystemd Service (Linux):")
        print(f"  sudo cp roraftp.service /etc/systemd/system/")
        print(f"  sudo systemctl enable roraftp")
        print(f"  sudo systemctl start roraftp")
    
    print(f"\nDocumentation:")
    print(f"  README.md - Quick start guide")
    print(f"  DEPLOYMENT.md - Detailed deployment guide")
    print(f"  API.md - API documentation")
    
    if tests_passed:
        print_status("All systems ready for deployment!", "success")
    else:
        print_status("Deployment complete with warnings", "warning")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
