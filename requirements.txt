# RoraFTP - Universal File Server Dependencies
# These dependencies are automatically installed by the server when needed
# You can also install them manually with: pip install -r requirements.txt

# QR Code generation (optional but recommended)
qrcode[pil]>=7.0.0

# FTP server functionality (optional)
pyftpdlib>=1.5.6

# Core dependencies (included with Python 3.6+)
# - http.server (built-in)
# - socketserver (built-in)
# - threading (built-in)
# - pathlib (built-in)
# - json (built-in)
# - logging (built-in)
# - argparse (built-in)
# - socket (built-in)
# - time (built-in)
# - subprocess (built-in)
# - webbrowser (built-in)
# - urllib.parse (built-in)
# - cgi (built-in)
# - gzip (built-in)
