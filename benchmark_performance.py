#!/usr/bin/env python3
"""
⚡ RoraFTP Performance Benchmarking Suite
Version: 2.0.0

Comprehensive performance testing and optimization recommendations:
- File transfer speed testing
- Concurrent user simulation
- Memory usage profiling
- Response time analysis
- Throughput measurement
"""

import time
import requests
import threading
import concurrent.futures
import psutil
import os
import sys
import json
import statistics
from pathlib import Path
import tempfile
import io

class PerformanceBenchmark:
    """Comprehensive performance benchmarking for RoraFTP."""
    
    def __init__(self, server_url="http://localhost:8888"):
        self.server_url = server_url
        self.results = {
            'response_times': [],
            'throughput_tests': [],
            'memory_usage': [],
            'concurrent_performance': [],
            'file_transfer_speeds': [],
            'recommendations': []
        }
        self.process = psutil.Process()
    
    def measure_response_time(self, endpoint="/", iterations=100):
        """Measure response time for specific endpoint."""
        print(f"📊 Testing response time for {endpoint} ({iterations} iterations)...")
        
        times = []
        errors = 0
        
        for i in range(iterations):
            try:
                start_time = time.time()
                response = requests.get(f"{self.server_url}{endpoint}", timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    times.append(end_time - start_time)
                else:
                    errors += 1
                    
            except Exception as e:
                errors += 1
            
            if (i + 1) % 20 == 0:
                print(f"   Progress: {i + 1}/{iterations}")
        
        if times:
            avg_time = statistics.mean(times)
            median_time = statistics.median(times)
            min_time = min(times)
            max_time = max(times)
            
            result = {
                'endpoint': endpoint,
                'iterations': iterations,
                'average_ms': avg_time * 1000,
                'median_ms': median_time * 1000,
                'min_ms': min_time * 1000,
                'max_ms': max_time * 1000,
                'errors': errors,
                'success_rate': (len(times) / iterations) * 100
            }
            
            self.results['response_times'].append(result)
            
            print(f"   ✅ Average: {avg_time*1000:.2f}ms")
            print(f"   📈 Median: {median_time*1000:.2f}ms")
            print(f"   ⚡ Min: {min_time*1000:.2f}ms")
            print(f"   🐌 Max: {max_time*1000:.2f}ms")
            print(f"   ❌ Errors: {errors}")
            
            # Add recommendations
            if avg_time > 0.5:
                self.results['recommendations'].append(
                    f"Slow response time for {endpoint} ({avg_time*1000:.0f}ms) - consider optimization"
                )
            
            return result
        else:
            print(f"   ❌ All requests failed for {endpoint}")
            return None
    
    def test_concurrent_users(self, num_users=20, requests_per_user=10):
        """Test performance under concurrent load."""
        print(f"👥 Testing concurrent users ({num_users} users, {requests_per_user} requests each)...")
        
        def user_simulation():
            """Simulate a single user's requests."""
            user_times = []
            user_errors = 0
            
            for _ in range(requests_per_user):
                try:
                    start_time = time.time()
                    response = requests.get(self.server_url, timeout=15)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        user_times.append(end_time - start_time)
                    else:
                        user_errors += 1
                except:
                    user_errors += 1
            
            return user_times, user_errors
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(user_simulation) for _ in range(num_users)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Aggregate results
        all_times = []
        total_errors = 0
        
        for user_times, user_errors in results:
            all_times.extend(user_times)
            total_errors += user_errors
        
        total_requests = num_users * requests_per_user
        successful_requests = len(all_times)
        
        if all_times:
            avg_response_time = statistics.mean(all_times)
            throughput = successful_requests / total_time
            
            result = {
                'concurrent_users': num_users,
                'requests_per_user': requests_per_user,
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'total_errors': total_errors,
                'success_rate': (successful_requests / total_requests) * 100,
                'average_response_time_ms': avg_response_time * 1000,
                'throughput_rps': throughput,
                'total_duration_s': total_time
            }
            
            self.results['concurrent_performance'].append(result)
            
            print(f"   ✅ Success Rate: {result['success_rate']:.1f}%")
            print(f"   ⚡ Throughput: {throughput:.2f} requests/second")
            print(f"   📊 Avg Response: {avg_response_time*1000:.2f}ms")
            print(f"   ⏱️  Total Time: {total_time:.2f}s")
            
            # Add recommendations
            if result['success_rate'] < 95:
                self.results['recommendations'].append(
                    f"Low success rate under load ({result['success_rate']:.1f}%) - consider scaling"
                )
            
            if throughput < 10:
                self.results['recommendations'].append(
                    f"Low throughput ({throughput:.1f} rps) - consider performance optimization"
                )
            
            return result
        else:
            print(f"   ❌ All concurrent requests failed")
            return None
    
    def test_file_upload_performance(self, file_sizes=[1024, 10240, 102400, 1048576]):
        """Test file upload performance with different file sizes."""
        print(f"📤 Testing file upload performance...")
        
        for size in file_sizes:
            print(f"   Testing {size} bytes ({size/1024:.1f}KB)...")
            
            # Create test file
            test_data = b'x' * size
            
            try:
                start_time = time.time()
                
                files = {'file': ('test.txt', io.BytesIO(test_data), 'text/plain')}
                response = requests.post(f"{self.server_url}/upload", files=files, timeout=30)
                
                end_time = time.time()
                duration = end_time - start_time
                
                if response.status_code == 200:
                    speed_mbps = (size / (1024 * 1024)) / duration
                    
                    result = {
                        'file_size_bytes': size,
                        'file_size_kb': size / 1024,
                        'upload_time_s': duration,
                        'speed_mbps': speed_mbps,
                        'success': True
                    }
                    
                    self.results['file_transfer_speeds'].append(result)
                    
                    print(f"     ✅ {duration:.2f}s ({speed_mbps:.2f} MB/s)")
                    
                    # Add recommendations
                    if speed_mbps < 1:
                        self.results['recommendations'].append(
                            f"Slow upload speed for {size/1024:.0f}KB files ({speed_mbps:.2f} MB/s)"
                        )
                else:
                    print(f"     ❌ Upload failed (HTTP {response.status_code})")
                    
            except Exception as e:
                print(f"     ❌ Upload error: {e}")
    
    def monitor_memory_usage(self, duration=60):
        """Monitor memory usage over time."""
        print(f"💾 Monitoring memory usage for {duration} seconds...")
        
        memory_samples = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024)
                memory_samples.append({
                    'timestamp': time.time() - start_time,
                    'memory_mb': memory_mb
                })
                time.sleep(1)
            except:
                break
        
        if memory_samples:
            memory_values = [sample['memory_mb'] for sample in memory_samples]
            
            result = {
                'duration_s': duration,
                'samples': len(memory_samples),
                'initial_memory_mb': memory_values[0],
                'final_memory_mb': memory_values[-1],
                'peak_memory_mb': max(memory_values),
                'average_memory_mb': statistics.mean(memory_values),
                'memory_increase_mb': memory_values[-1] - memory_values[0]
            }
            
            self.results['memory_usage'].append(result)
            
            print(f"   📊 Initial: {result['initial_memory_mb']:.1f} MB")
            print(f"   📈 Peak: {result['peak_memory_mb']:.1f} MB")
            print(f"   📊 Final: {result['final_memory_mb']:.1f} MB")
            print(f"   📊 Increase: {result['memory_increase_mb']:.1f} MB")
            
            # Add recommendations
            if result['memory_increase_mb'] > 100:
                self.results['recommendations'].append(
                    f"High memory increase ({result['memory_increase_mb']:.1f} MB) - potential memory leak"
                )
            
            if result['peak_memory_mb'] > 500:
                self.results['recommendations'].append(
                    f"High peak memory usage ({result['peak_memory_mb']:.1f} MB) - consider optimization"
                )
            
            return result
        
        return None
    
    def run_comprehensive_benchmark(self):
        """Run all performance tests."""
        print("⚡ Starting RoraFTP Performance Benchmark")
        print("=" * 50)
        
        # Check server availability
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            if response.status_code != 200:
                print("❌ Server not responding properly")
                return None
        except:
            print("❌ Cannot connect to server")
            return None
        
        print("✅ Server is responding\n")
        
        # Run tests
        self.measure_response_time("/", 50)
        self.measure_response_time("/health", 30)
        
        self.test_concurrent_users(10, 5)
        self.test_concurrent_users(20, 3)
        
        self.test_file_upload_performance([1024, 10240, 102400])
        
        self.monitor_memory_usage(30)
        
        # Generate report
        return self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive performance report."""
        print("\n" + "=" * 50)
        print("📊 Performance Benchmark Report")
        print("=" * 50)
        
        # Response time summary
        if self.results['response_times']:
            print("\n⚡ Response Time Summary:")
            for result in self.results['response_times']:
                print(f"   {result['endpoint']}: {result['average_ms']:.1f}ms avg")
        
        # Concurrent performance
        if self.results['concurrent_performance']:
            print("\n👥 Concurrent Performance:")
            for result in self.results['concurrent_performance']:
                print(f"   {result['concurrent_users']} users: {result['throughput_rps']:.1f} rps")
        
        # File transfer speeds
        if self.results['file_transfer_speeds']:
            print("\n📤 Upload Performance:")
            for result in self.results['file_transfer_speeds']:
                print(f"   {result['file_size_kb']:.0f}KB: {result['speed_mbps']:.2f} MB/s")
        
        # Memory usage
        if self.results['memory_usage']:
            print("\n💾 Memory Usage:")
            for result in self.results['memory_usage']:
                print(f"   Peak: {result['peak_memory_mb']:.1f} MB")
                print(f"   Increase: {result['memory_increase_mb']:.1f} MB")
        
        # Recommendations
        if self.results['recommendations']:
            print("\n💡 Performance Recommendations:")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        # Save detailed results
        with open('performance_benchmark.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: performance_benchmark.json")
        
        return self.results

if __name__ == '__main__':
    benchmark = PerformanceBenchmark()
    benchmark.run_comprehensive_benchmark()
